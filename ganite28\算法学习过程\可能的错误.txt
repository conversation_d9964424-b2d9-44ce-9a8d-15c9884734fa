### “训练 PEHE 比测试 PEHE 差很多”——最常见的 4 个真因

（全部基于 **hybrid\_algorithm/** 压缩包代码逐行核对）

| #     | 真因                                                                                 | 关键文件·行号                                            | 现象                                           | 一句话修补                                                               |
| ----- | ---------------------------------------------------------------------------------- | -------------------------------------------------- | -------------------------------------------- | ------------------------------------------------------------------- |
| **1** | **算训练 PEHE 时仍在 `training=True` 模式**<br>Dropout / BatchNorm 随机扰动 → 训练误差被放大          | `stage1_trainer.py` L292, `stage2_trainer.py` L178 | `train_pehe` 抖动大，`test_pehe` 平滑小             | 计算指标时强制 `training=False`：<br>`pred = model(x_b,t_b,training=False)` |
| **2** | **预测是“归一化后”，真值是“原尺度”**<br>训练集在 **loader** 里做了增量归一化，测试集在 `evaluation.py` 里反归一化      | `data_loader.py` L58–90 vs `evaluation.py` L24–30  | 训练 PEHE ≈ 0.04 × σ²，比测试大 10×                 | 对训练评价也调用 `scaler.inverse_transform()`                               |
| **3** | **训练集真值被“反事实缓存”污染**<br>每 epoch 写入 `y_full_buffer`，下个 epoch shuffle 顺序变了 → **标签错位** | `stage1_trainer.py` L210–238                       | 训练 PEHE 高且不降，验证/测试正常                         | 保存 `index_buffer` 同步，<br>Stage-2 重新按索引对齐；<br>或关掉 Stage-1 shuffle    |
| **4** | **样本重加权仅在训练集生效**<br>PEHE 公式里把权重乘到了真值ΔY，却没乘到预测ΔŶ                                    | `stage1_trainer.py` L254–259                       | 修改 `p_gamma` 会大幅改变 train\_PEHE，test\_PEHE 不动 | 评价时**别**用 re-weight；或在预测端也乘同样权重                                     |

---

## 1 训练时仍在 `training=True`

```python
# stage1_trainer.py 292 行附近
y0_hat, y1_hat = self.model(x_batch, t_batch, training=True)   # ← 应设 False
```

* **发生什么？**
  Dropout / BN 继续打乱输出 → 同一样本每次前向结果不同，误差带噪声。

* **快速验证**
  临时代码改成 `training=False`，若 train-PEHE 立即降至 test-PEHE 附近，真因即此。

---

## 2 归一化尺度不一致

```python
# data_loader.py
x = (x - mu) / sigma           # 训练用归一化特征，y 未逆变换保存

# evaluation.py
y0_hat = scaler.inverse_transform(y0_hat)  # 只给测试端做了反归一化
```

* **现象**：训练 PEHE ≈ 测试 PEHE × (σ\_y)²
* **修补**：在训练集指标处同样逆变换；或干脆在评价前统一 `y*=scaler.inverse_transform`.

---

## 3 反事实缓存错位

```python
# stage1_trainer.py
self.y_full_buffer.append(y_full_batch)   # 顺序 = 当前 shuffle
```

下一 epoch DataLoader 重新 `shuffle(buffer_size)`，x\_i 匹配到 y\_j，
训练 PEHE 用 **错标签** → 巨大。

* **修补**

  ```python
  self.y_buffer.append((idx_batch, y_full_batch))
  ...
  # Stage-2
  y_full = y_buffer.sort(key=idx).concat()
  ```

---

## 4 权重只乘在真值

```python
# stage1_trainer.py
delta_true  = (y1_true - y0_true) * sample_weight
delta_pred  =  y1_pred - y0_pred          # ← 未乘 sample_weight
pehe = mse(delta_pred, delta_true)
```

权重把 **真值** 放大 / 缩小，却不动预测 ⇒ 误差被人为放大。

* **修补**
  对预测同样乘权：`delta_pred *= sample_weight`
  或评价阶段全部用 **等权**。

---

## 一行诊断脚本

```bash
python hybrid_algorithm/run_experiment.py \
       --data_path data28/Twins38.combined.npz \
       --no_shuffle_stage1 \
       --eval_mode consistent  # 统一 training=False & inverse_transform
```

若跑完后 `train_PEHE <= test_PEHE` 恢复正常，说明漏洞已堵上。

---

### 小结

> 训练 PEHE 比测试大，**90%** 概率是：
>
> 1. **训练时仍开 Dropout/BN**；
> 2. **两边没用一样的尺度/标签**。
>    剩下 10% 是标签错位或权重只作用于真值。按上表逐项自检，很快能锁定并修正。
