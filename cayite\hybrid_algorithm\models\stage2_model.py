#!/usr/bin/env python3
"""
阶段二模型：ITE估计精炼器
完全按照VGANITE的Stage2设计：ITE_Generator + ITE_WGAN_GPCritic
"""

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import numpy as np

class Stage2Generator(keras.Model):
    """
    阶段二生成器：ITE估计精炼器
    完全按照VGANITE的ITE_Generator设计
    """

    def __init__(self, config, name='stage2_generator'):
        super(Stage2Generator, self).__init__(name=name)

        self.config = config

        # 完全按照VGANITE的ITE_Generator设计（wgangp.py第251-254行）
        self.I_h1 = layers.Dense(units=config.h_dim, activation=tf.nn.relu, name=f'{name}_h1')
        self.I_h2 = layers.Dense(units=config.h_dim, activation=tf.nn.relu, name=f'{name}_h2')
        self.I_h3 = layers.Dense(units=config.h_dim, activation=tf.nn.relu, name=f'{name}_h3')
        self.I_logit = layers.Dense(units=2, name=f'{name}_logit')  # 输出[Y(0), Y(1)]的logits

    def call(self, x, training=None):
        """
        前向传播（完全按照VGANITE的ITE_Generator.call）

        Args:
            x: 原始特征 [batch_size, x_dim]

        Returns:
            I_logit: [Y(0), Y(1)]的logits [batch_size, 2]
        """
        # 完全按照VGANITE的实现（wgangp.py第258-262行）
        I_h1 = self.I_h1(x)
        I_h2 = self.I_h2(I_h1)
        I_h3 = self.I_h3(I_h2)
        I_logit = self.I_logit(I_h3)

        return I_logit

class Stage2Critic(keras.Model):
    """
    阶段二评论家：WGAN-GP Critic
    完全按照VGANITE的ITE_WGAN_GPCritic设计
    """

    def __init__(self, config, name='stage2_critic'):
        super(Stage2Critic, self).__init__(name=name)

        self.config = config

        # 完全按照VGANITE的ITE_WGAN_GPCritic设计（wgangp.py第268-277行）
        self.DI_h1 = layers.Dense(units=config.h_dim * 4, name=f'{name}_h1')
        self.norm1 = layers.LayerNormalization(name=f'{name}_norm1')
        self.act1 = layers.LeakyReLU(alpha=0.2, name=f'{name}_act1')

        self.DI_h2 = layers.Dense(units=config.h_dim * 2, name=f'{name}_h2')
        self.norm2 = layers.LayerNormalization(name=f'{name}_norm2')
        self.act2 = layers.LeakyReLU(alpha=0.2, name=f'{name}_act2')

        self.DI_h3 = layers.Dense(units=config.h_dim, name=f'{name}_h3')
        self.norm3 = layers.LayerNormalization(name=f'{name}_norm3')
        self.act3 = layers.LeakyReLU(alpha=0.2, name=f'{name}_act3')

        self.DI_critic = layers.Dense(units=1, name=f'{name}_critic')

    def call(self, x, y_pair, training=None):
        """
        前向传播（完全按照VGANITE的ITE_WGAN_GPCritic.call）

        Args:
            x: 原始特征 [batch_size, x_dim]
            y_pair: 潜在结果对 [batch_size, 2]

        Returns:
            critic_score: 评判分数 [batch_size, 1]
        """
        # 完全按照VGANITE的实现（wgangp.py第280-285行）
        inputs = tf.concat([x, y_pair], axis=1)
        DI_h1 = self.act1(self.norm1(self.DI_h1(inputs)))
        DI_h2 = self.act2(self.norm2(self.DI_h2(DI_h1)))
        DI_h3 = self.act3(self.norm3(self.DI_h3(DI_h1)))  # 注意：这里使用DI_h1，与VGANITE完全一致
        DI_critic = self.DI_critic(DI_h3)

        return DI_critic

class Stage2Model(keras.Model):
    """阶段二模型：ITE估计精炼器"""
    
    def __init__(self, config, name='stage2_model'):
        super(Stage2Model, self).__init__(name=name)
        
        self.config = config
        
        # 阶段二生成器
        self.generator = Stage2Generator(config, name='stage2_generator')
        
        # 阶段二评论家
        self.critic = Stage2Critic(config, name='stage2_critic')
    
    def call(self, inputs, training=None):
        """
        前向传播
        
        Args:
            inputs: 拼接后的[x, y_bar_1, z_I]
        
        Returns:
            精炼后的潜在结果logits
        """
        return self.generator(inputs, training=training)
    
    def generate(self, x, y_bar_1, z_I, training=None):
        """生成精炼后的潜在结果"""
        inputs = tf.concat([x, y_bar_1, z_I], axis=1)
        return self.generator(inputs, training=training)
    
    def discriminate(self, x, y_pair, training=None):
        """评判潜在结果对的真实性"""
        return self.critic(x, y_pair, training=training)

    def generate_refined_outcomes(self, x, y_bar_stage1=None, training=False):
        """生成精炼的潜在结果（完全按照VGANITE的阶段二生成）"""
        # 使用阶段二生成器生成精炼结果
        refined_logits = self.generator(x, training=training)
        refined_probs = tf.nn.sigmoid(refined_logits)
        return refined_probs

def gradient_penalty(critic, x_batch, real_y_pair, fake_y_pair):
    """
    梯度惩罚函数（完全按照VGANITE的实现，wgangp.py第288-306行）
    """
    batch_size_gp = tf.shape(x_batch)[0]
    alpha = tf.random.uniform([batch_size_gp, 1], 0., 1.)
    interpolated_y_pair = alpha * real_y_pair + (1 - alpha) * fake_y_pair

    with tf.GradientTape() as tape_gp:
        tape_gp.watch(interpolated_y_pair)
        critic_interpolated = critic(x_batch, interpolated_y_pair)

    grads = tape_gp.gradient(critic_interpolated, interpolated_y_pair)
    # 完全按照VGANITE的安全检查
    if grads is None:
        tf.print("Warning: Gradients for GP calculation are None.")
        return tf.constant(0.0, dtype=tf.float32)

    # 完全按照VGANITE的梯度范数计算（添加epsilon）
    norm = tf.sqrt(tf.reduce_sum(tf.square(grads), axis=[1]) + 1e-8)
    gp = tf.reduce_mean((norm - 1.0) ** 2)

    return gp
