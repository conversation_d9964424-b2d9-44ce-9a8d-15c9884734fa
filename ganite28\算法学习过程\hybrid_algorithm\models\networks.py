"""
基础网络组件：定义表征网络、生成器、判别器等基础组件
"""

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import numpy as np

class RepresentationNetwork(keras.Model):
    """表征网络：学习I(X)、C(X)、A(X)表征"""
    
    def __init__(self, input_dim, output_dim, num_layers=3, hidden_dim=None, 
                 activation='relu', batch_norm=False, dropout_rate=0.1, name='representation'):
        super(RepresentationNetwork, self).__init__(name=name)
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.num_layers = num_layers
        self.batch_norm = batch_norm
        self.dropout_rate = dropout_rate
        
        # 计算每层的维度
        if hidden_dim is None:
            dims = np.linspace(input_dim, output_dim, num_layers + 1).astype(int)
        else:
            dims = [input_dim] + [hidden_dim] * (num_layers - 1) + [output_dim]
        
        # 构建网络层
        self.dense_layers = []
        self.batch_norm_layers = []
        self.dropout_layers = []
        
        for i in range(num_layers):
            # 全连接层
            self.dense_layers.append(
                layers.Dense(dims[i+1], 
                           kernel_initializer='glorot_uniform',
                           name=f'{name}_dense_{i}')
            )
            
            # 批归一化层
            if batch_norm:
                self.batch_norm_layers.append(
                    layers.BatchNormalization(name=f'{name}_bn_{i}')
                )
            
            # Dropout层
            if dropout_rate > 0:
                self.dropout_layers.append(
                    layers.Dropout(dropout_rate, name=f'{name}_dropout_{i}')
                )
        
        # 激活函数
        if activation == 'relu':
            self.activation = tf.nn.relu
        elif activation == 'tanh':
            self.activation = tf.nn.tanh
        elif activation == 'elu':
            self.activation = tf.nn.elu
        else:
            self.activation = tf.nn.relu
    
    def call(self, inputs, training=None):
        x = inputs
        
        for i in range(self.num_layers):
            # 全连接层
            x = self.dense_layers[i](x)
            
            # 批归一化
            if self.batch_norm:
                x = self.batch_norm_layers[i](x, training=training)
            
            # 激活函数（最后一层不使用激活函数）
            if i < self.num_layers - 1:
                x = self.activation(x)
            
            # Dropout
            if self.dropout_rate > 0 and i < self.num_layers - 1:
                x = self.dropout_layers[i](x, training=training)
        
        return x
    
    def get_weights_for_orthogonal_loss(self):
        """获取用于正交损失计算的权重"""
        weights = []
        for layer in self.dense_layers:
            weights.append(layer.kernel)
        return weights

class Generator(keras.Model):
    """生成器：生成潜在结果Y(0)和Y(1)"""
    
    def __init__(self, input_dim, hidden_dim=64, num_layers=3, 
                 activation='leaky_relu', batch_norm=True, dropout_rate=0.2, name='generator'):
        super(Generator, self).__init__(name=name)
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.batch_norm = batch_norm
        self.dropout_rate = dropout_rate
        
        # 构建网络层
        self.dense_layers = []
        self.batch_norm_layers = []
        self.dropout_layers = []
        
        # 隐藏层
        for i in range(num_layers - 1):
            self.dense_layers.append(
                layers.Dense(hidden_dim, name=f'{name}_dense_{i}')
            )
            
            if batch_norm:
                self.batch_norm_layers.append(
                    layers.BatchNormalization(name=f'{name}_bn_{i}')
                )
            
            if dropout_rate > 0:
                self.dropout_layers.append(
                    layers.Dropout(dropout_rate, name=f'{name}_dropout_{i}')
                )
        
        # 输出层：生成Y(0)和Y(1)的logits
        self.output_layer = layers.Dense(2, name=f'{name}_output')
        
        # 激活函数
        if activation == 'leaky_relu':
            self.activation = layers.LeakyReLU(alpha=0.2)
        elif activation == 'relu':
            self.activation = layers.ReLU()
        elif activation == 'tanh':
            self.activation = layers.Activation('tanh')
        else:
            self.activation = layers.LeakyReLU(alpha=0.2)
    
    def call(self, inputs, training=None):
        x = inputs
        
        # 隐藏层
        for i in range(self.num_layers - 1):
            x = self.dense_layers[i](x)
            
            if self.batch_norm:
                x = self.batch_norm_layers[i](x, training=training)
            
            x = self.activation(x)
            
            if self.dropout_rate > 0:
                x = self.dropout_layers[i](x, training=training)
        
        # 输出层
        logits = self.output_layer(x)
        
        return logits

class Discriminator(keras.Model):
    """判别器：区分真实和生成的结果"""
    
    def __init__(self, input_dim, hidden_dim=64, num_layers=3, 
                 activation='leaky_relu', name='discriminator'):
        super(Discriminator, self).__init__(name=name)
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        # 构建网络层
        self.dense_layers = []
        
        # 隐藏层
        for i in range(num_layers - 1):
            self.dense_layers.append(
                layers.Dense(hidden_dim, name=f'{name}_dense_{i}')
            )
        
        # 输出层
        self.output_layer = layers.Dense(1, name=f'{name}_output')
        
        # 激活函数
        if activation == 'leaky_relu':
            self.activation = layers.LeakyReLU(alpha=0.2)
        elif activation == 'relu':
            self.activation = layers.ReLU()
        else:
            self.activation = layers.LeakyReLU(alpha=0.2)
    
    def call(self, treatment, outcome, generated_outcome, training=None):
        """
        Args:
            treatment: 处理变量 T
            outcome: 真实结果 Y
            generated_outcome: 生成的结果 [Y(0), Y(1)]
        """
        # 构建判别器输入：y_bar = T * Y(1) + (1-T) * Y(0)
        y_bar = treatment * generated_outcome[:, 1:2] + (1 - treatment) * generated_outcome[:, 0:1]
        
        # 拼接输入
        x = tf.concat([treatment, outcome, y_bar], axis=1)
        
        # 前向传播
        for i in range(self.num_layers - 1):
            x = self.dense_layers[i](x)
            x = self.activation(x)
        
        # 输出logit
        logit = self.output_layer(x)
        
        return logit

class WGANCritic(keras.Model):
    """WGAN-GP的判别器（Critic）"""
    
    def __init__(self, input_dim, hidden_dim=64, num_layers=3, name='critic'):
        super(WGANCritic, self).__init__(name=name)
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        # 构建网络层
        self.dense_layers = []
        self.layer_norm_layers = []
        
        # 隐藏层
        for i in range(num_layers - 1):
            self.dense_layers.append(
                layers.Dense(hidden_dim, name=f'{name}_dense_{i}')
            )
            self.layer_norm_layers.append(
                layers.LayerNormalization(name=f'{name}_ln_{i}')
            )
        
        # 输出层
        self.output_layer = layers.Dense(1, name=f'{name}_output')
        
        # 激活函数
        self.activation = layers.LeakyReLU(alpha=0.2)
    
    def call(self, x_features, y_outcomes, training=None):
        """
        Args:
            x_features: 输入特征
            y_outcomes: 结果 [Y(0), Y(1)]
        """
        # 拼接输入
        x = tf.concat([x_features, y_outcomes], axis=1)
        
        # 前向传播
        for i in range(self.num_layers - 1):
            x = self.dense_layers[i](x)
            x = self.layer_norm_layers[i](x, training=training)
            x = self.activation(x)
        
        # 输出（不使用激活函数）
        output = self.output_layer(x)
        
        return output

class VGANITEDiscriminator(keras.Model):
    """
    VGANITE风格的判别器：判断"真假混合"的潜在结果对
    完全按照VGANITE的设计：输入y_bar，输出对treatment的预测
    """

    def __init__(self, hidden_dim=64, name='vganite_discriminator'):
        super(VGANITEDiscriminator, self).__init__(name=name)

        # 按照VGANITE的判别器设计
        self.D_h1 = layers.Dense(units=10, activation='relu', name=f'{name}_h1')
        self.D_h2 = layers.Dense(units=5, activation='relu', name=f'{name}_h2')
        self.D_logit = layers.Dense(units=1, name=f'{name}_logit')  # 输出treatment预测的logit

    def call(self, t, y, y_hat_logits, training=None):
        """
        前向传播（完全按照VGANITE的设计）

        Args:
            t: 真实treatment [batch_size, 1]
            y: 真实factual outcome [batch_size, 1]
            y_hat_logits: 生成器输出的潜在结果logits [batch_size, 2]

        Returns:
            D_logit: 对treatment的预测logit [batch_size, 1]
        """
        # 将logits转换为概率（与VGANITE一致）
        y_hat_prob = tf.nn.sigmoid(y_hat_logits)
        y_hat_prob = tf.reshape(y_hat_prob, [-1, 2])

        y = tf.reshape(y, [-1, 1])
        t = tf.reshape(t, [-1, 1])

        # 构建y_bar：真实factual + 生成的counterfactual（严格按照您的规格）
        # 计算生成的反事实结果 Y_hat_CF_1
        Y_hat_CF_1 = (1. - t) * tf.reshape(y_hat_prob[:, 1], [-1, 1]) + t * tf.reshape(y_hat_prob[:, 0], [-1, 1])

        # 执行拼接替换（严格按照您的规格）
        # 对于处理组的样本 (T=1): y_bar = concatenate([Y_hat_0_1, Y_F], axis=1)
        # 对于控制组的样本 (T=0): y_bar = concatenate([Y_F, Y_hat_1_1], axis=1)
        input0 = (1. - t) * y + t * tf.reshape(y_hat_prob[:, 0], [-1, 1])  # 第一个位置
        input1 = t * y + (1. - t) * tf.reshape(y_hat_prob[:, 1], [-1, 1])  # 第二个位置
        y_bar = tf.concat([input0, input1], axis=1)  # Shape: [batch, 2]

        # 基于y_bar预测treatment（VGANITE的判别任务）
        D_h1 = self.D_h1(y_bar)
        D_h2 = self.D_h2(D_h1)
        D_logit = self.D_logit(D_h2)  # 预测treatment的logit

        return D_logit
