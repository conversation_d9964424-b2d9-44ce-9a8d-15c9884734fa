{"generator_8_8_8": {"mA": 8, "mB": 8, "mC": 8, "mD": 2, "ABC_list": [], "sc": 1.0, "sh": 0.0, "init_seed": 4, "size": 3000, "file": "Syn_cont", "binary": "T", "num": 10, "random_coef": "F", "coef_t_AB": [8, 8, 11, 13, 8, 8, 16, 11, 13, 16, 11, 16, 8, 8, 8, 8], "coef_y1_BC": [11, 8, 8, 16, 13, 8, 8, 16, 13, 16, 11, 11, 8, 8, 8, 8], "coef_y2_BC": [8, 8, 8, 8, 16, 13, 11, 11, 13, 11, 16, 11, 8, 8, 8, 8], "coef_devide_1": 10, "coef_devide_2": 10, "coef_devide_3": 10, "proba_or_logit": "logit", "use_one": "F"}, "generator_16_16_16": {"mA": 16, "mB": 16, "mC": 16, "mD": 2, "ABC_list": [], "sc": 1.0, "sh": 0.0, "init_seed": 4, "size": 3000, "file": "Syn_cont", "binary": "T", "num": 10, "random_coef": "F", "coef_t_AB": [8, 8, 11, 13, 8, 8, 16, 11, 13, 16, 11, 16, 8, 8, 8, 8, 8, 8, 11, 13, 8, 8, 16, 11, 13, 16, 11, 16, 8, 8, 8, 8], "coef_y1_BC": [11, 8, 8, 16, 13, 8, 8, 16, 13, 16, 11, 11, 8, 8, 8, 8, 11, 8, 8, 16, 13, 8, 8, 16, 13, 16, 11, 11, 8, 8, 8, 8], "coef_y2_BC": [8, 8, 8, 8, 16, 13, 11, 11, 13, 11, 16, 11, 8, 8, 8, 8, 8, 8, 8, 8, 16, 13, 11, 11, 13, 11, 16, 11, 8, 8, 8, 8], "coef_devide_1": 10, "coef_devide_2": 10, "coef_devide_3": 10, "proba_or_logit": "logit", "use_one": "F"}}