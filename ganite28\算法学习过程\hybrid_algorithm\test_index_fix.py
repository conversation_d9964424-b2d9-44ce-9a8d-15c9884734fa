"""
测试索引修复效果
验证训练集和测试集评估结果是否合理
"""

import numpy as np
import tensorflow as tf
from npzdata import load_twins_npz_data, get_npz_data_info
from evaluation import evaluate_model_on_multiple_datasets
from models.stage1_model import Stage1Model
from config import get_config

def test_index_fix():
    """测试索引修复效果"""
    print("="*60)
    print("测试索引修复效果")
    print("="*60)
    
    try:
        # 获取配置
        config = get_config()
        config.x_dim = 38
        
        # 加载数据
        print("1. 加载NPZ数据...")
        data_loader = load_twins_npz_data(
            data_path='../data28/Twins38.combined.npz',
            train_valid_test=[63, 27, 10],
            seed=123,
            ind=0
        )
        
        # 验证索引修复
        print("\n2. 验证索引修复...")
        print(f"训练集索引范围: {data_loader.train['I'][0]} ~ {data_loader.train['I'][-1]}")
        print(f"验证集索引范围: {data_loader.valid['I'][0]} ~ {data_loader.valid['I'][-1]}")
        print(f"测试集索引范围: {data_loader.test['I'][0]} ~ {data_loader.test['I'][-1]}")
        
        # 检查索引是否重叠
        train_indices = set(data_loader.train['I'])
        valid_indices = set(data_loader.valid['I'])
        test_indices = set(data_loader.test['I'])
        
        train_valid_overlap = train_indices & valid_indices
        train_test_overlap = train_indices & test_indices
        valid_test_overlap = valid_indices & test_indices
        
        print(f"\n索引重叠检查:")
        print(f"  训练集-验证集重叠: {len(train_valid_overlap)} 个")
        print(f"  训练集-测试集重叠: {len(train_test_overlap)} 个")
        print(f"  验证集-测试集重叠: {len(valid_test_overlap)} 个")
        
        if len(train_valid_overlap) == 0 and len(train_test_overlap) == 0 and len(valid_test_overlap) == 0:
            print("  ✅ 索引修复成功！无重叠")
        else:
            print("  ❌ 索引仍有重叠")
            return False
        
        # 创建简单模型进行测试
        print("\n3. 创建测试模型...")
        model = Stage1Model(config)
        
        # 初始化样本权重
        total_samples = data_loader.num
        model.build_sample_weights(total_samples)
        
        # 验证样本权重大小
        print(f"\n样本权重验证:")
        print(f"  权重数组大小: {model.sample_weights.shape}")
        print(f"  总样本数: {total_samples}")
        print(f"  最大索引: {max(data_loader.test['I'])}")
        
        if model.sample_weights.shape[0] > max(data_loader.test['I']):
            print("  ✅ 样本权重数组足够大，支持所有索引")
        else:
            print("  ❌ 样本权重数组太小")
            return False
        
        # 测试索引访问
        print("\n4. 测试索引访问...")
        try:
            # 测试训练集索引
            train_weights = tf.gather(model.sample_weights, data_loader.train['I'][:10])
            print(f"  训练集权重访问: ✅ shape={train_weights.shape}")
            
            # 测试验证集索引
            valid_weights = tf.gather(model.sample_weights, data_loader.valid['I'][:10])
            print(f"  验证集权重访问: ✅ shape={valid_weights.shape}")
            
            # 测试测试集索引
            test_weights = tf.gather(model.sample_weights, data_loader.test['I'][:10])
            print(f"  测试集权重访问: ✅ shape={test_weights.shape}")
            
        except Exception as e:
            print(f"  ❌ 索引访问失败: {e}")
            return False
        
        # 验证数据一致性
        print("\n5. 验证数据一致性...")
        
        # 检查真实ATE
        train_ate = np.mean(data_loader.train['mu1'] - data_loader.train['mu0'])
        valid_ate = np.mean(data_loader.valid['mu1'] - data_loader.valid['mu0'])
        test_ate = np.mean(data_loader.test['mu1'] - data_loader.test['mu0'])
        
        print(f"  训练集真实ATE: {train_ate:.6f}")
        print(f"  验证集真实ATE: {valid_ate:.6f}")
        print(f"  测试集真实ATE: {test_ate:.6f}")
        
        # 检查处理比例
        train_treatment_ratio = np.mean(data_loader.train['t'])
        valid_treatment_ratio = np.mean(data_loader.valid['t'])
        test_treatment_ratio = np.mean(data_loader.test['t'])
        
        print(f"  训练集处理比例: {train_treatment_ratio:.3f}")
        print(f"  验证集处理比例: {valid_treatment_ratio:.3f}")
        print(f"  测试集处理比例: {test_treatment_ratio:.3f}")
        
        print("\n" + "="*60)
        print("✅ 索引修复验证完成！")
        print("="*60)
        print("\n建议:")
        print("1. 重新训练模型以验证修复效果")
        print("2. 观察训练集和测试集评估结果是否合理")
        print("3. 训练集结果应该不再比测试集差很多")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_before_after():
    """比较修复前后的预期效果"""
    print("\n" + "="*60)
    print("修复前后对比")
    print("="*60)
    
    print("修复前的问题:")
    print("  - 训练集索引: 0~3319")
    print("  - 验证集索引: 0~1422")
    print("  - 测试集索引: 0~526")
    print("  - 索引重叠导致样本权重错乱")
    print("  - 训练集PEHE (0.311) > 测试集PEHE (0.278)")
    
    print("\n修复后的改进:")
    print("  - 训练集索引: 0~3319")
    print("  - 验证集索引: 3320~4742")
    print("  - 测试集索引: 4743~5270")
    print("  - 索引全局唯一，无重叠")
    print("  - 样本权重正确对应")
    print("  - 预期训练集结果会改善")

if __name__ == "__main__":
    # 运行测试
    success = test_index_fix()
    
    if success:
        compare_before_after()
    
    print("\n测试完成!")
