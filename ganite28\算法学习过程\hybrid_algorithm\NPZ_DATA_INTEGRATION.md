# NPZ数据集成说明

## 🎯 改进目标

将hybrid_algorithm从使用VGANITE的数据加载方式改为使用DeR-CFR的NPZ数据格式，实现：
1. **数据格式统一**：使用`data28\Twins38.combined.npz`数据集
2. **划分方式一致**：采用DeR-CFR的63:27:10训练/验证/测试集划分
3. **变量格式兼容**：支持DeR-CFR的完整变量集合 `[x, t, y, ycf, mu0, mu1]`
4. **无预处理**：完全按照DeR-CFR方式，不做数据标准化
5. **阈值处理**：实现DeR-CFR的阈值计算逻辑

## 🔧 实现的改进

### **1. 新增NPZ数据加载模块 (`npzdata.py`)**

#### **核心类：Load_NPZ_Data**
```python
class Load_NPZ_Data(object):
    """完全按照DeR-CFR\utils.py的Load_Data类实现"""
    
    def load_npz(self, file_path, ind=0):
        """加载NPZ文件，读取DeR-CFR格式的变量"""
        data = np.load(file_path)
        
        # 按照DeR-CFR的格式读取变量
        t = data['t'][:, ind:ind+1]           # 处理变量
        y = data['yf'][:, ind:ind+1]          # 事实结果
        ycf = data['ycf'][:, ind:ind+1]       # 反事实结果
        mu0 = data['mu0'][:, ind:ind+1]       # 潜在结果Y(0)
        mu1 = data['mu1'][:, ind:ind+1]       # 潜在结果Y(1)
        x = data['x'][:, :, ind]              # 协变量特征
    
    def split_data(self, train_valid_test=[63, 27, 10]):
        """按照DeR-CFR的方式划分数据集"""
        # 63:27:10 = 63%训练集 + 27%验证集 + 10%测试集
```

#### **便捷函数**
```python
def load_twins_npz_data(data_path, train_valid_test=[7, 2, 1], seed=123, ind=0):
    """一键加载Twins NPZ数据"""

def preprocess_npz_data(data_dict, normalize_x=True, normalize_y=False):
    """预处理NPZ数据（特征标准化）"""

def get_npz_data_info(data_loader):
    """获取数据集信息"""
```

### **2. 更新主程序 (`main.py`)**

#### **数据加载函数改进**
```python
def load_and_preprocess_data(config):
    """使用NPZ数据加载器，完全按照DeR-CFR的方式"""
    
    # 加载NPZ数据
    data_loader = load_twins_npz_data(
        data_path=config.data_path,  # 'data28/Twins38.combined.npz'
        train_valid_test=[7, 2, 1],  # DeR-CFR标准划分
        seed=config.seed,
        ind=0
    )
    
    # 返回三个数据集（训练/验证/测试）
    return train_data, valid_data, test_data
```

#### **数据格式兼容性**
```python
# 构造与原hybrid_algorithm兼容的数据格式
train_data = (
    train_processed['x'],      # 特征
    train_processed['t'],      # 处理变量
    train_processed['y'],      # 事实结果
    train_potential_y          # 潜在结果 [mu0, mu1]
)
```

### **3. 更新配置文件 (`config.py`)**

```python
# 默认配置更新
self.data_name = 'twins'  # 数据集名称
self.data_path = 'data28/Twins38.combined.npz'  # NPZ数据路径
```

### **4. 新增测试脚本 (`test_npz_loading.py`)**

提供完整的数据加载测试功能：
- NPZ文件读取测试
- 数据格式验证
- 预处理功能测试
- 模型兼容性测试

## 📊 **数据变量对比**

### **DeR-CFR原始变量**
| 变量 | 含义 | 形状 | 用途 |
|------|------|------|------|
| `x` | 协变量特征 | `[n, d]` | 输入特征 |
| `t` | 处理变量 | `[n, 1]` | 0/1二元处理 |
| `y` | 事实结果 | `[n, 1]` | 观测到的结果 |
| `ycf` | 反事实结果 | `[n, 1]` | 未观测的反事实 |
| `mu0` | 潜在结果Y(0) | `[n, 1]` | 不接受处理的结果 |
| `mu1` | 潜在结果Y(1) | `[n, 1]` | 接受处理的结果 |

### **hybrid_algorithm兼容格式**
| 变量 | 来源 | 形状 | 说明 |
|------|------|------|------|
| `train_x` | `x` | `[n_train, d]` | 标准化后的特征 |
| `train_t` | `t` | `[n_train, 1]` | 处理变量 |
| `train_y` | `y` | `[n_train, 1]` | 事实结果 |
| `train_potential_y` | `[mu0, mu1]` | `[n_train, 2]` | 潜在结果矩阵 |

## 🔄 **数据流程**

### **1. 数据读取流程**
```
NPZ文件 (data28/Twins38.combined.npz)
    ↓ load_npz()
DeR-CFR格式变量 [x, t, y, ycf, mu0, mu1]
    ↓ split_data()
训练/验证/测试集 (7:2:1)
    ↓ preprocess_npz_data()
标准化数据
    ↓ 格式转换
hybrid_algorithm兼容格式
```

### **2. 数据预处理**
```python
# 特征标准化（使用训练集统计量）
x_mean = np.mean(train_x, axis=0)
x_std = np.std(train_x, axis=0) + 1e-8
train_x_normalized = (train_x - x_mean) / x_std

# 验证集和测试集使用相同的标准化参数
valid_x_normalized = (valid_x - x_mean) / x_std
test_x_normalized = (test_x - x_mean) / x_std
```

## ✅ **关键优势**

### **1. 数据格式统一**
- 使用与DeR-CFR完全相同的NPZ数据格式
- 支持完整的变量集合，包括反事实结果
- 便于与DeR-CFR进行公平比较

### **2. 划分方式一致**
- 采用DeR-CFR的7:2:1标准划分
- 避免了数据泄漏问题
- 支持验证集进行模型选择

### **3. 向后兼容**
- 保持与原hybrid_algorithm的接口兼容
- 数据格式自动转换
- 无需修改模型代码

### **4. 可扩展性**
- 支持多重复实验（通过`ind`参数）
- 灵活的数据预处理选项
- 完整的测试和验证功能

## 🚀 **使用方法**

### **1. 基本使用**
```python
from npzdata import load_twins_npz_data
from config import get_config

# 获取配置
config = get_config()

# 加载数据
data_loader = load_twins_npz_data(
    data_path=config.data_path,
    train_valid_test=[7, 2, 1],
    seed=config.seed,
    ind=0
)

# 访问数据
train_data = data_loader.train  # 字典格式
valid_data = data_loader.valid
test_data = data_loader.test
```

### **2. 测试数据加载**
```bash
cd hybrid_algorithm
python test_npz_loading.py
```

### **3. 运行完整训练**
```bash
cd hybrid_algorithm
python main.py --data_path data28/Twins38.combined.npz
```

## 📋 **注意事项**

1. **数据文件路径**：确保`data28/Twins38.combined.npz`文件存在
2. **内存使用**：NPZ文件可能较大，注意内存使用情况
3. **随机种子**：使用相同的种子确保结果可重复
4. **数据验证**：运行测试脚本验证数据加载正确性

这种改进使得hybrid_algorithm能够使用与DeR-CFR完全相同的数据格式和划分方式，确保了实验的公平性和结果的可比性。
