#!/usr/bin/env python3
"""
对比旧实现（只用第一层）vs 新实现（权重矩阵连乘）的差异
"""

import os
import sys
import tensorflow as tf
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from models.stage1_model import Stage1Model

def compute_orthogonal_loss_old_way(model):
    """
    旧的实现方式：只使用第一层权重（修改前的逻辑）
    """
    # 获取权重矩阵
    w_I = model.rep_I.get_weights_for_orthogonal_loss()
    w_C = model.rep_C.get_weights_for_orthogonal_loss()
    w_A = model.rep_A.get_weights_for_orthogonal_loss()

    # 旧实现：只使用第一层权重，避免矩阵连乘的形状问题
    w_I_layer0 = w_I[0] if len(w_I) > 0 else tf.zeros([1, 1])
    w_C_layer0 = w_C[0] if len(w_C) > 0 else tf.zeros([1, 1])
    w_A_layer0 = w_A[0] if len(w_A) > 0 else tf.zeros([1, 1])

    # 计算特征贡献度（按照DeR-CFR原版的简化实现）
    w_I_mean = tf.reduce_mean(tf.abs(w_I_layer0), axis=1)
    w_C_mean = tf.reduce_mean(tf.abs(w_C_layer0), axis=1)
    w_A_mean = tf.reduce_mean(tf.abs(w_A_layer0), axis=1)

    # 正交损失：最小化点积
    orthogonal_loss = (
        tf.reduce_sum(w_I_mean * w_C_mean) +
        tf.reduce_sum(w_I_mean * w_A_mean) +
        tf.reduce_sum(w_C_mean * w_A_mean)
    )

    # 支撑损失：防止权重全为零
    support_loss = (
        tf.square(tf.reduce_sum(w_I_mean) - 1.0) +
        tf.square(tf.reduce_sum(w_C_mean) - 1.0) +
        tf.square(tf.reduce_sum(w_A_mean) - 1.0)
    )

    return orthogonal_loss + support_loss, w_I_mean, w_C_mean, w_A_mean

def test_old_vs_new_implementation():
    """对比旧实现 vs 新实现"""
    print("="*80)
    print("对比旧实现（只用第一层）vs 新实现（权重矩阵连乘）")
    print("="*80)
    
    # 创建配置
    config = Config()
    config.x_dim = 10
    config.rep_dim = 8
    config.rep_layers = 3  # 3层网络
    config.batch_norm = False
    config.dropout_rate = 0.0
    config.select_layer = 0  # 使用所有层（新实现）
    
    # 创建模型
    model = Stage1Model(config)
    
    # 创建测试数据
    batch_size = 32
    x = tf.random.normal([batch_size, config.x_dim])
    t = tf.random.uniform([batch_size, 1], maxval=2, dtype=tf.int32)
    t = tf.cast(t, tf.float32)
    y = tf.random.uniform([batch_size, 1], maxval=2, dtype=tf.int32)
    y = tf.cast(y, tf.float32)
    
    # 初始化模型
    _ = model([x, t, y], training=False)
    
    print(f"网络配置:")
    print(f"  - 输入维度: {config.x_dim}")
    print(f"  - 表征维度: {config.rep_dim}")
    print(f"  - 网络层数: {config.rep_layers}")
    
    # 获取权重信息
    w_I = model.rep_I.get_weights_for_orthogonal_loss()
    print(f"\n权重矩阵信息:")
    for i, w in enumerate(w_I):
        print(f"  - 第{i}层权重形状: {w.shape}")
        print(f"  - 第{i}层权重范数: {tf.norm(w).numpy():.6f}")
    
    print("\n" + "="*50)
    print("旧实现（只使用第一层权重）")
    print("="*50)
    
    old_loss, old_w_I_mean, old_w_C_mean, old_w_A_mean = compute_orthogonal_loss_old_way(model)
    
    print(f"正交损失: {old_loss.numpy():.6f}")
    print(f"I网络特征贡献度前5个值: {old_w_I_mean.numpy()[:5]}")
    print(f"C网络特征贡献度前5个值: {old_w_C_mean.numpy()[:5]}")
    print(f"A网络特征贡献度前5个值: {old_w_A_mean.numpy()[:5]}")
    
    print("\n" + "="*50)
    print("新实现（权重矩阵连乘，select_layer=0）")
    print("="*50)
    
    new_loss = model.compute_orthogonal_loss()
    
    # 手动计算新实现的特征贡献度以便对比
    w_I_sum = w_I[0]
    w_C_sum = model.rep_C.get_weights_for_orthogonal_loss()[0]
    w_A_sum = model.rep_A.get_weights_for_orthogonal_loss()[0]
    
    for i in range(1, len(w_I)):
        w_I_sum = tf.matmul(w_I_sum, w_I[i])
        w_C_sum = tf.matmul(w_C_sum, model.rep_C.get_weights_for_orthogonal_loss()[i])
        w_A_sum = tf.matmul(w_A_sum, model.rep_A.get_weights_for_orthogonal_loss()[i])
    
    new_w_I_mean = tf.reduce_mean(tf.abs(w_I_sum), axis=1)
    new_w_C_mean = tf.reduce_mean(tf.abs(w_C_sum), axis=1)
    new_w_A_mean = tf.reduce_mean(tf.abs(w_A_sum), axis=1)
    
    print(f"正交损失: {new_loss.numpy():.6f}")
    print(f"I网络特征贡献度前5个值: {new_w_I_mean.numpy()[:5]}")
    print(f"C网络特征贡献度前5个值: {new_w_C_mean.numpy()[:5]}")
    print(f"A网络特征贡献度前5个值: {new_w_A_mean.numpy()[:5]}")
    
    print("\n" + "="*50)
    print("差异分析")
    print("="*50)
    
    loss_diff = abs(new_loss.numpy() - old_loss.numpy())
    print(f"正交损失差异: {loss_diff:.6f}")
    
    w_I_diff = tf.reduce_mean(tf.abs(new_w_I_mean - old_w_I_mean))
    w_C_diff = tf.reduce_mean(tf.abs(new_w_C_mean - old_w_C_mean))
    w_A_diff = tf.reduce_mean(tf.abs(new_w_A_mean - old_w_A_mean))
    
    print(f"I网络特征贡献度平均差异: {w_I_diff.numpy():.6f}")
    print(f"C网络特征贡献度平均差异: {w_C_diff.numpy():.6f}")
    print(f"A网络特征贡献度平均差异: {w_A_diff.numpy():.6f}")
    
    if loss_diff > 1e-6:
        print("✓ 新实现确实产生了不同的结果！")
        print("  这证明权重矩阵连乘确实改变了特征贡献度的计算")
    else:
        print("✗ 新实现与旧实现结果相同")
        print("  可能是网络层数太少或权重初始化的问题")
    
    print("\n" + "="*50)
    print("权重矩阵连乘效果验证")
    print("="*50)
    
    print("第一层权重矩阵范数:")
    print(f"  - I网络: {tf.norm(w_I[0]).numpy():.6f}")
    print(f"  - C网络: {tf.norm(model.rep_C.get_weights_for_orthogonal_loss()[0]).numpy():.6f}")
    print(f"  - A网络: {tf.norm(model.rep_A.get_weights_for_orthogonal_loss()[0]).numpy():.6f}")
    
    print("连乘后权重矩阵范数:")
    print(f"  - I网络: {tf.norm(w_I_sum).numpy():.6f}")
    print(f"  - C网络: {tf.norm(w_C_sum).numpy():.6f}")
    print(f"  - A网络: {tf.norm(w_A_sum).numpy():.6f}")
    
    # 计算连乘的"放大"效应
    i_amplification = tf.norm(w_I_sum).numpy() / tf.norm(w_I[0]).numpy()
    c_amplification = tf.norm(w_C_sum).numpy() / tf.norm(model.rep_C.get_weights_for_orthogonal_loss()[0]).numpy()
    a_amplification = tf.norm(w_A_sum).numpy() / tf.norm(model.rep_A.get_weights_for_orthogonal_loss()[0]).numpy()
    
    print("权重矩阵连乘的放大效应:")
    print(f"  - I网络: {i_amplification:.3f}x")
    print(f"  - C网络: {c_amplification:.3f}x")
    print(f"  - A网络: {a_amplification:.3f}x")

if __name__ == '__main__':
    # 设置TensorFlow日志级别
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
    
    test_old_vs_new_implementation()
    
    print("\n" + "="*80)
    print("测试完成")
    print("="*80)
