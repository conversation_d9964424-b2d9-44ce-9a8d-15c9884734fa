"""
测试DeR-CFR风格的批次生成器
"""

import numpy as np
import tensorflow as tf
from npzdata import load_twins_npz_data, create_derc_batch_generator, get_npz_data_info

def test_derc_batch_generator():
    """测试DeR-CFR风格的批次生成器"""
    print("="*60)
    print("测试DeR-CFR风格的批次生成器")
    print("="*60)
    
    try:
        # 加载数据
        print("1. 加载NPZ数据...")
        data_loader = load_twins_npz_data(
            data_path='../data28/Twins38.combined.npz',
            train_valid_test=[63, 27, 10],
            seed=123,
            ind=0
        )
        
        # 获取数据信息
        data_info = get_npz_data_info(data_loader)
        print(f"\n数据信息:")
        print(f"  总样本数: {data_info['total_samples']}")
        print(f"  训练样本: {data_info['train_samples']}")
        print(f"  验证样本: {data_info['valid_samples']}")
        print(f"  测试样本: {data_info['test_samples']}")
        print(f"  特征维度: {data_info['x_dim']}")
        
        # 创建批次生成器
        print("\n2. 创建DeR-CFR风格的批次生成器...")
        batch_size = 256  # 使用DeR-CFR的默认批次大小
        batch_generator = create_derc_batch_generator(data_loader, batch_size)
        
        # 测试批次生成
        print("\n3. 测试批次生成...")
        print(f"批次大小: {batch_generator.batch_size}")
        print(f"每个epoch的批次数: {batch_generator.batch_num}")
        
        # 生成几个批次进行测试
        print("\n4. 生成测试批次...")
        for i in range(3):
            x, t, y, I, ycf, mu0, mu1 = batch_generator.batch.__next__()
            
            print(f"\n批次 {i+1}:")
            print(f"  x shape: {x.shape}")
            print(f"  t shape: {t.shape}")
            print(f"  y shape: {y.shape}")
            print(f"  I shape: {I.shape}")
            print(f"  ycf shape: {ycf.shape}")
            print(f"  mu0 shape: {mu0.shape}")
            print(f"  mu1 shape: {mu1.shape}")
            
            # 验证数据类型和范围
            print(f"  t 范围: [{np.min(t):.3f}, {np.max(t):.3f}]")
            print(f"  y 范围: [{np.min(y):.3f}, {np.max(y):.3f}]")
            print(f"  处理比例: {np.mean(t):.3f}")
            print(f"  结果比例: {np.mean(y):.3f}")
            
            # 验证索引的有效性
            assert np.all(I >= 0) and np.all(I < data_info['train_samples']), "索引超出范围"
            print(f"  索引范围: [{np.min(I)}, {np.max(I)}] ✓")
        
        # 测试TensorFlow兼容性
        print("\n5. 测试TensorFlow兼容性...")
        x, t, y, I, ycf, mu0, mu1 = batch_generator.batch.__next__()
        
        # 转换为TensorFlow张量
        x_tf = tf.cast(x, tf.float32)
        t_tf = tf.cast(t, tf.float32)
        y_tf = tf.cast(y, tf.float32)
        I_tf = tf.cast(I, tf.int32)
        
        print(f"  TensorFlow张量转换成功:")
        print(f"    x_tf: {x_tf.shape}, dtype: {x_tf.dtype}")
        print(f"    t_tf: {t_tf.shape}, dtype: {t_tf.dtype}")
        print(f"    y_tf: {y_tf.shape}, dtype: {y_tf.dtype}")
        print(f"    I_tf: {I_tf.shape}, dtype: {I_tf.dtype}")
        
        # 测试连续生成多个epoch
        print("\n6. 测试连续生成...")
        total_batches = batch_generator.batch_num * 2  # 两个epoch
        unique_indices = set()
        
        for i in range(total_batches):
            x, t, y, I, ycf, mu0, mu1 = batch_generator.batch.__next__()
            unique_indices.update(I.flatten())
            
            if i % batch_generator.batch_num == 0:
                print(f"  Epoch {i // batch_generator.batch_num + 1} 开始")
        
        print(f"  总共生成了 {total_batches} 个批次")
        print(f"  遇到的唯一索引数: {len(unique_indices)}")
        print(f"  预期训练样本数: {data_info['train_samples']}")
        
        # 验证数据打乱
        print("\n7. 验证数据打乱...")
        first_batch_indices = []
        for i in range(3):
            # 重新创建生成器
            new_generator = create_derc_batch_generator(data_loader, batch_size)
            x, t, y, I, ycf, mu0, mu1 = new_generator.batch.__next__()
            first_batch_indices.append(I[:5].copy())  # 记录前5个索引
        
        # 检查是否有不同的打乱结果
        indices_different = not np.array_equal(first_batch_indices[0], first_batch_indices[1])
        print(f"  数据打乱验证: {'✓' if indices_different else '✗'}")
        
        print("\n" + "="*60)
        print("✅ DeR-CFR风格批次生成器测试成功!")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_generator_performance():
    """测试批次生成器的性能"""
    print("\n" + "="*60)
    print("测试批次生成器性能")
    print("="*60)
    
    try:
        # 加载数据
        data_loader = load_twins_npz_data(
            data_path='../data28/Twins38.combined.npz',
            train_valid_test=[63, 27, 10],
            seed=123,
            ind=0
        )
        
        batch_size = 256
        batch_generator = create_derc_batch_generator(data_loader, batch_size)
        
        # 性能测试
        import time
        
        print(f"生成 100 个批次的性能测试...")
        start_time = time.time()
        
        for i in range(100):
            x, t, y, I, ycf, mu0, mu1 = batch_generator.batch.__next__()
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"  总时间: {elapsed_time:.4f} 秒")
        print(f"  平均每批次: {elapsed_time/100:.4f} 秒")
        print(f"  每秒批次数: {100/elapsed_time:.2f}")
        
        print("✅ 性能测试完成!")
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")

if __name__ == "__main__":
    # 运行测试
    success = test_derc_batch_generator()
    
    if success:
        test_batch_generator_performance()
    
    print("\n测试完成!")
