# DeR-CFR-VGANITE 融合算法

## 项目概述

本项目实现了一个融合DeR-CFR和VGANITE两种算法优势的新型因果推断算法，用于个体治疗效果(ITE)估计。

## 算法特点

### 核心创新
1. **两阶段融合架构**：
   - 阶段一：DeR-CFR增强的表征分解 + GAN生成
   - 阶段二：WGAN-GP精炼反事实结果

2. **技术优势**：
   - 结合DeR-CFR的精确分解能力
   - 利用VGANITE的强大生成能力
   - 端到端梯度回传优化

### 算法流程

#### 阶段一：表征分解与初步生成
1. 使用三个表征网络学习I(X)、C(X)、A(X)
2. 应用DeR-CFR的损失函数约束（L_A、L_I、L_O、L_C_B）
3. 用生成器G1替换回归头，生成初步的潜在结果
4. 判别器D1提供对抗训练信号

#### 阶段二：WGAN-GP精炼
1. 冻结阶段一的表征网络
2. 使用WGAN-GP架构精炼反事实结果
3. 加入循环一致性损失确保质量

## 文件结构

```
hybrid_algorithm/
├── README.md                 # 项目说明
├── config.py                 # 配置文件
├── models/
│   ├── __init__.py
│   ├── stage1_model.py       # 阶段一模型（DeR-CFR + GAN）
│   ├── stage2_model.py       # 阶段二模型（WGAN-GP）
│   └── networks.py           # 基础网络组件
├── training/
│   ├── __init__.py
│   ├── stage1_trainer.py     # 阶段一训练器
│   ├── stage2_trainer.py     # 阶段二训练器
│   └── losses.py             # 损失函数定义
├── data/
│   ├── __init__.py
│   ├── data_loader.py        # 数据加载器
│   └── synthetic_data.py     # 合成数据生成
├── utils/
│   ├── __init__.py
│   ├── metrics.py            # 评估指标
│   └── visualization.py     # 可视化工具
├── main.py                   # 主执行脚本
└── experiments/
    ├── run_synthetic.py      # 合成数据实验
    └── run_real_data.py      # 真实数据实验
```

## 快速开始

### 1. 环境配置

**⚠️ 重要：环境兼容性说明**

为避免conda/pip混用导致的二进制兼容性问题，我们提供了多种安装选项：

#### 方法一：自动安装（推荐）
```bash
python setup.py
```
脚本会引导你选择最适合的安装方式。

#### 方法二：安全的conda环境
```bash
conda env create -f environment_safe.yaml
conda activate hybrid_algorithm
```

#### 方法三：纯conda环境
```bash
conda env create -f environment_conda_only.yaml
conda activate hybrid_algorithm_conda
```

#### 方法四：纯pip环境（不推荐）
```bash
pip install -r requirements.txt
```

#### 环境诊断
安装后运行诊断脚本检查兼容性：
```bash
python diagnose_environment.py
```

### 2. 运行合成数据实验
```bash
python experiments/run_synthetic.py
```

### 3. 运行真实数据实验
```bash
python experiments/run_real_data.py --data_path your_data.csv
```

## 算法参数

### 阶段一参数
- `rep_dim`: 表征维度 (默认: 20)
- `rep_layers`: 表征网络层数 (默认: 3)
- `p_alpha`: 调整变量损失权重 (默认: 1.0)
- `p_beta`: 工具变量损失权重 (默认: 1.0)
- `p_mu`: 正交正则化权重 (默认: 1.0)
- `p_gamma`: 平衡损失权重 (默认: 1.0)

### 阶段二参数
- `h_dim`: 隐藏层维度 (默认: 64)
- `lambda_gp`: 梯度惩罚权重 (默认: 10.0)
- `n_critic`: 判别器更新次数 (默认: 5)

## 评估指标

- **PEHE** (Precision in Estimation of Heterogeneous Effect): 个体治疗效果估计精度
- **ATE** (Average Treatment Effect): 平均治疗效果
- **ATT** (Average Treatment Effect on Treated): 处理组平均治疗效果

## 引用

如果您使用了本算法，请引用相关论文：

```bibtex
@article{der_cfr,
  title={Learning Decomposed Representations for Treatment Effect Estimation},
  author={...},
  journal={...},
  year={...}
}

@article{vganite,
  title={VGANITE: Variational Generative Adversarial Networks for Individualized Treatment Effect},
  author={...},
  journal={...},
  year={...}
}
```

## 故障排除

### 环境兼容性问题

1. **DLL加载失败 / ImportError**：
   ```bash
   # 诊断环境
   python diagnose_environment.py

   # 重新创建纯conda环境
   conda env remove -n hybrid_algorithm
   conda env create -f environment_safe.yaml
   ```

2. **计算结果异常**：
   ```bash
   # 检查包的安装来源
   python -c "import numpy; print(numpy.__file__)"
   python -c "import scipy; print(scipy.__file__)"

   # 如果路径混合了conda和pip，需要重新安装
   ```

3. **TensorFlow GPU问题**：
   ```bash
   # 检查CUDA版本
   nvidia-smi

   # 检查TensorFlow GPU支持
   python -c "import tensorflow as tf; print(tf.config.list_physical_devices('GPU'))"
   ```

### 常见问题

1. **导入错误**：确保vganite模块在Python路径中
2. **CUDA错误**：检查GPU驱动和CUDA版本兼容性
3. **内存不足**：减小批次大小或模型维度
4. **收敛问题**：调整学习率和损失权重

### 调试模式

```bash
python main.py --stage1_epochs 10 --stage2_epochs 5 --eval_freq 5
```

### 环境重建指南

如果遇到严重的兼容性问题：

1. **完全清理环境**：
   ```bash
   conda env remove -n hybrid_algorithm
   conda clean --all
   ```

2. **重新安装**：
   ```bash
   python setup.py
   ```

3. **验证安装**：
   ```bash
   python diagnose_environment.py
   python test_setup.py
   ```
