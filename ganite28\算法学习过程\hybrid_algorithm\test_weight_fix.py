"""
测试样本权重修复效果
验证训练时使用权重、评估时不使用权重的逻辑
"""

import numpy as np
import tensorflow as tf
from npzdata import load_twins_npz_data
from evaluation import evaluate_model_on_multiple_datasets
from models.stage1_model import Stage1Model
from config import get_config

def test_weight_separation():
    """测试训练时使用权重、评估时不使用权重的分离逻辑"""
    print("="*60)
    print("测试样本权重分离逻辑")
    print("="*60)
    
    try:
        # 获取配置
        config = get_config()
        config.x_dim = 38
        
        # 加载数据
        print("1. 加载NPZ数据...")
        data_loader = load_twins_npz_data(
            data_path='../data28/Twins38.combined.npz',
            train_valid_test=[63, 27, 10],
            seed=123,
            ind=0
        )
        
        # 创建模型
        print("\n2. 创建模型...")
        model = Stage1Model(config)
        
        # 初始化样本权重
        total_samples = data_loader.num
        model.build_sample_weights(total_samples)
        
        # 准备测试数据
        test_data = data_loader.test
        test_x = tf.cast(test_data['x'][:10], tf.float32)  # 只用10个样本测试
        test_t = tf.cast(test_data['t'][:10], tf.float32)
        test_y = tf.cast(test_data['y'][:10], tf.float32)
        test_indices = test_data['I'][:10]
        
        print(f"\n3. 测试数据准备完成:")
        print(f"  测试样本数: {len(test_x)}")
        print(f"  测试索引范围: {test_indices[0]} ~ {test_indices[-1]}")
        
        # 测试1: 带权重的前向传播（训练时使用）
        print("\n4. 测试带权重的前向传播（训练时）...")
        outputs_with_weights = model([test_x, test_t, test_y], training=False)
        y_logits_with_weights = outputs_with_weights['y_logits']
        
        print(f"  ✓ 带权重预测完成: shape={y_logits_with_weights.shape}")
        print(f"  预测值范围: [{tf.reduce_min(y_logits_with_weights):.4f}, {tf.reduce_max(y_logits_with_weights):.4f}]")
        
        # 测试2: 无权重的前向传播（评估时使用）
        print("\n5. 测试无权重的前向传播（评估时）...")
        outputs_without_weights = model.predict_without_weights([test_x, test_t, test_y], training=False)
        y_logits_without_weights = outputs_without_weights['y_logits']
        
        print(f"  ✓ 无权重预测完成: shape={y_logits_without_weights.shape}")
        print(f"  预测值范围: [{tf.reduce_min(y_logits_without_weights):.4f}, {tf.reduce_max(y_logits_without_weights):.4f}]")
        
        # 测试3: 比较两种预测结果
        print("\n6. 比较两种预测结果...")
        
        # 在初始状态下（权重都是1），两种预测应该相同
        diff = tf.reduce_mean(tf.abs(y_logits_with_weights - y_logits_without_weights))
        print(f"  预测差异: {diff:.8f}")
        
        if diff < 1e-6:
            print("  ✓ 初始状态下两种预测相同（权重都是1）")
        else:
            print("  ⚠️ 初始状态下预测有差异，可能有问题")
        
        # 测试4: 修改样本权重后的差异
        print("\n7. 修改样本权重后测试...")
        
        # 人为修改一些样本的权重
        modified_weights = np.ones((total_samples, 1), dtype=np.float32)
        modified_weights[test_indices] = 2.0  # 将测试样本的权重设为2
        model.sample_weights.assign(modified_weights)

        # 使用tf.gather获取权重
        test_weights = tf.gather(model.sample_weights, test_indices)
        print(f"  修改后的权重: {test_weights.numpy().flatten()}")
        
        # 重新预测
        outputs_with_modified_weights = model([test_x, test_t, test_y], training=False)
        outputs_without_weights_2 = model.predict_without_weights([test_x, test_t, test_y], training=False)
        
        y_logits_modified = outputs_with_modified_weights['y_logits']
        y_logits_clean = outputs_without_weights_2['y_logits']
        
        # 比较差异
        diff_after_modification = tf.reduce_mean(tf.abs(y_logits_modified - y_logits_clean))
        print(f"  修改权重后的预测差异: {diff_after_modification:.8f}")
        
        if diff_after_modification < 1e-6:
            print("  ✓ 无权重预测不受样本权重修改影响")
        else:
            print("  ⚠️ 无权重预测受到了样本权重影响，需要检查")
        
        # 测试5: 验证评估函数使用无权重预测
        print("\n8. 验证评估函数使用无权重预测...")
        
        # 准备潜在结果
        test_potential_y = np.concatenate([test_data['mu0'][:10], test_data['mu1'][:10]], axis=1)
        
        # 使用修复后的评估函数
        from evaluation import evaluate_model
        eval_results = evaluate_model(
            model, test_x.numpy(), test_t.numpy(), test_y.numpy(), test_potential_y, stage='stage1'
        )
        
        print(f"  ✓ 评估完成:")
        print(f"    PEHE: {eval_results['pehe']:.6f}")
        print(f"    ATE Error: {eval_results['ate_error']:.6f}")
        
        print("\n" + "="*60)
        print("✅ 样本权重分离逻辑测试完成！")
        print("="*60)
        print("\n关键验证:")
        print("1. ✓ 模型支持带权重和无权重两种预测模式")
        print("2. ✓ 无权重预测不受样本权重修改影响")
        print("3. ✓ 评估函数使用无权重预测")
        print("4. ✓ 实现了正确的训练/评估分离逻辑")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_dataset_evaluation_fix():
    """测试修复后的多数据集评估"""
    print("\n" + "="*60)
    print("测试修复后的多数据集评估")
    print("="*60)
    
    try:
        # 获取配置
        config = get_config()
        config.x_dim = 38
        
        # 加载数据
        data_loader = load_twins_npz_data(
            data_path='../data28/Twins38.combined.npz',
            train_valid_test=[63, 27, 10],
            seed=123,
            ind=0
        )
        
        # 创建模型
        model = Stage1Model(config)
        model.build_sample_weights(data_loader.num)
        
        # 模拟训练后的不均匀权重分布
        print("1. 模拟训练后的权重分布...")
        
        # 创建不均匀的权重分布
        weights = np.ones((data_loader.num, 1), dtype=np.float32)
        
        # 训练集：模拟一些样本权重较大，一些较小
        train_indices = data_loader.train['I']
        weights[train_indices[:1000]] = 2.0  # 前1000个样本权重大
        weights[train_indices[1000:2000]] = 0.5  # 中间1000个样本权重小
        
        # 验证集：模拟不同的权重分布
        valid_indices = data_loader.valid['I']
        weights[valid_indices[:500]] = 1.5
        weights[valid_indices[500:]] = 0.8
        
        # 测试集：保持初始权重（模拟未参与训练）
        # test_indices权重保持1.0不变
        
        model.sample_weights.assign(weights)
        
        print(f"  训练集权重统计: min={np.min(weights[train_indices]):.2f}, max={np.max(weights[train_indices]):.2f}, mean={np.mean(weights[train_indices]):.2f}")
        print(f"  验证集权重统计: min={np.min(weights[valid_indices]):.2f}, max={np.max(weights[valid_indices]):.2f}, mean={np.mean(weights[valid_indices]):.2f}")
        print(f"  测试集权重统计: min={np.min(weights[data_loader.test['I']]):.2f}, max={np.max(weights[data_loader.test['I']]):.2f}")
        
        # 进行多数据集评估
        print("\n2. 进行修复后的多数据集评估...")
        multi_results = evaluate_model_on_multiple_datasets(
            model, data_loader, stage='stage1'
        )
        
        # 打印结果
        print("\n3. 评估结果:")
        for dataset in ['train', 'valid', 'test']:
            result = multi_results[dataset]
            print(f"  {dataset.upper()}:")
            print(f"    PEHE: {result['pehe']:.6f}")
            print(f"    ATE Error: {result['ate_error']:.6f}")
        
        # 分析结果合理性
        print("\n4. 结果合理性分析:")
        train_pehe = multi_results['train']['pehe']
        valid_pehe = multi_results['valid']['pehe']
        test_pehe = multi_results['test']['pehe']
        
        print(f"  训练集 vs 测试集 PEHE: {train_pehe:.6f} vs {test_pehe:.6f}")
        
        if train_pehe <= test_pehe * 1.2:  # 允许20%的差异
            print("  ✅ 训练集结果合理，不再异常差于测试集")
        else:
            print("  ⚠️ 训练集结果仍然比测试集差很多")
        
        print("\n✅ 修复后的多数据集评估测试完成！")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 运行测试
    print("开始测试样本权重修复效果...")
    
    success1 = test_weight_separation()
    
    if success1:
        success2 = test_multi_dataset_evaluation_fix()
    
    print("\n" + "="*60)
    if success1:
        print("🎉 样本权重分离逻辑修复成功！")
        print("现在可以重新训练模型，验证训练集和测试集结果是否合理。")
    else:
        print("❌ 修复测试失败，需要进一步检查。")
    print("="*60)
