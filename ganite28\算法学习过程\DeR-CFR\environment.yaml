name: causal
channels:
  - http://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free
  - http://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
  - pytorch
  - http://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
  - http://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
  - http://mirrors.bfsu.edu.cn/anaconda/pkgs/main
  - http://mirrors.bfsu.edu.cn/anaconda/pkgs/free
  - http://mirrors.bfsu.edu.cn/anaconda/pkgs/r
  - http://mirrors.bfsu.edu.cn/anaconda/pkgs/pro
  - http://mirrors.bfsu.edu.cn/anaconda/pkgs/msys2
dependencies:
  - blas=1.0=mkl
  - coverage=4.4.1=py36_0
  - cython=0.26=py36_0
  - freetype=2.5.5=2
  - jbig=2.1=0
  - jpeg=9b=0
  - libpng=1.6.30=1
  - ninja=1.7.2=0
  - olefile=0.44=py36_0
  - pillow=4.2.1=py36_0
  - pycparser=2.18=py36_0
  - termcolor=1.1.0=py36_0
  - _libgcc_mutex=0.1=main
  - absl-py=0.15.0=pyhd3eb1b0_0
  - astor=0.8.1=py36h06a4308_0
  - c-ares=1.18.1=h7f8727e_0
  - ca-certificates=2021.10.26=h06a4308_2
  - certifi=2021.5.30=py36h06a4308_0
  - cffi=1.14.6=py36h400218f_0
  - cudatoolkit=10.0.130=0
  - cudnn=7.6.5=cuda10.0_0
  - google-pasta=0.2.0=pyhd3eb1b0_0
  - hdf5=1.10.6=hb1b8bf9_0
  - importlib-metadata=4.8.1=py36h06a4308_0
  - intel-openmp=2021.4.0=h06a4308_3561
  - keras-applications=1.0.8=py_1
  - keras-preprocessing=1.1.2=pyhd3eb1b0_0
  - ld_impl_linux-64=2.35.1=h7274673_9
  - libffi=3.3=he6710b0_2
  - libgcc-ng=9.1.0=hdf63c60_0
  - libstdcxx-ng=9.1.0=hdf63c60_0
  - markdown=3.3.4=py36h06a4308_0
  - mkl-service=2.3.0=py36he8ac12f_0
  - mkl_fft=1.3.0=py36h54f3939_0
  - mkl_random=1.1.1=py36h0573a6f_0
  - ncurses=6.3=h7f8727e_2
  - numpy=1.19.2=py36h54aff64_0
  - openssl=1.1.1m=h7f8727e_0
  - opt_einsum=3.3.0=pyhd3eb1b0_1
  - pip=21.2.2=py36h06a4308_0
  - python=3.6.10=h7579374_2
  - readline=8.1.2=h7f8727e_0
  - setuptools=58.0.4=py36h06a4308_0
  - six=1.16.0=pyhd3eb1b0_0
  - sqlite=3.37.0=hc218d9a_0
  - tensorboard=1.15.0=pyhb230dea_0
  - tensorflow-estimator=1.15.1=pyh2649769_0
  - tensorflow-gpu=1.15.0=h0d30ee6_0
  - tk=8.6.11=h1ccaba5_0
  - typing_extensions=********=pyh06a4308_0
  - werkzeug=0.16.1=py_0
  - wheel=0.37.1=pyhd3eb1b0_0
  - wrapt=1.12.1=py36h7b6447c_1
  - xz=5.2.5=h7b6447c_0
  - zipp=3.6.0=pyhd3eb1b0_0
  - zlib=1.2.11=h7f8727e_4
  - libtiff=4.0.6=3
  - _tflow_select=2.1.0=gpu
  - cupti=10.0.130=0
  - gast=0.2.2=py36_0
  - grpcio=1.36.1=py36h2157cd5_1
  - h5py=2.10.0=py36hd6299e0_1
  - libgfortran-ng=7.3.0=hdf63c60_0
  - libprotobuf=3.17.2=h4ff587b_1
  - mkl=2020.2=256
  - numpy-base=1.19.2=py36hfa32c7d_0
  - protobuf=3.17.2=py36h295c915_0
  - scipy=1.5.2=py36h0b6359f_0
  - tensorflow=1.15.0=gpu_py36h5a509aa_0
  - tensorflow-base=1.15.0=gpu_py36h9dcbed7_0
  - webencodings=0.5.1=py36_1
  - pytorch=1.2.0=py3.6_cuda10.0.130_cudnn7.6.2_0
  - torchvision=0.4.0=py36_cu100
  - pip:
    - backcall==0.2.0
    - cycler==0.11.0
    - decorator==5.1.1
    - entrypoints==0.3
    - future==0.18.2
    - ipykernel==5.5.6
    - ipython==7.16.2
    - ipython-genutils==0.2.0
    - jedi==0.17.2
    - joblib==1.1.0
    - jupyter-client==7.1.0
    - jupyter-core==4.9.1
    - keras==2.0.6
    - kiwisolver==1.3.1
    - llvmlite==0.36.0
    - matplotlib==3.2.2
    - nest-asyncio==1.5.4
    - numba==0.53.1
    - pandas==1.1.5
    - parso==0.7.1
    - pexpect==4.8.0
    - pickleshare==0.7.5
    - prompt-toolkit==3.0.24
    - ptyprocess==0.7.0
    - pygments==2.11.2
    - pyparsing==3.0.6
    - pysocks==1.7.1
    - python-dateutil==2.8.2
    - pytz==2021.3
    - pyyaml==6.0
    - pyzmq==22.3.0
    - scikit-learn==0.24.2
    - theano==1.0.5
    - threadpoolctl==3.0.0
    - torch==1.2.0
    - tornado==6.1
    - traitlets==4.3.3
    - wcwidth==0.2.5
prefix: /home/<USER>/anaconda3/envs/causal

