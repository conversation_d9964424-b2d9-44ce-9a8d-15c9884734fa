"""
阶段一训练器：DeR-CFR增强的表征分解 + GAN生成
"""

import tensorflow as tf
import numpy as np
import os
from tqdm import tqdm
import logging

from models.stage1_model import Stage1Model
from data_loader import batch_generator
from evaluation import (evaluate_model, print_evaluation_results,
                       evaluate_model_on_multiple_datasets, print_multi_dataset_results)
from npzdata import create_derc_batch_generator

class Stage1Trainer:
    """阶段一训练器"""
    
    def __init__(self, config):
        self.config = config
        
        # 设置随机种子
        tf.random.set_seed(config.seed)
        np.random.seed(config.seed)
        
        # 创建模型
        self.model = Stage1Model(config)
        
        # 创建优化器（阶段一GAN使用分离的学习率）
        self.optimizer_main = tf.keras.optimizers.Adam(
            learning_rate=config.learning_rate,  # 表征网络+生成器学习率
            beta_1=0.5,
            beta_2=0.9
        )

        self.optimizer_disc = tf.keras.optimizers.Adam(
            learning_rate=config.stage1_disc_lr,  # 判别器专用学习率
            beta_1=0.5,
            beta_2=0.9
        )
        
        # 如果启用样本重新加权，创建权重优化器
        if config.reweight_sample:
            self.optimizer_weights = tf.keras.optimizers.Adam(
                learning_rate=0.001  # wode样本权重
            )
        
        # 设置日志
        self.setup_logging()
        
        # 训练历史（更新以匹配新的损失结构）
        self.train_history = {
            'generator_loss': [],
            'discriminator_loss': [],
            'decomposition_loss': [],
            'adversarial_loss': [],
            'factual_loss': [],
            'enhanced_factual_loss': [],
            'balance_loss': [],
            'L_A': [],
            'L_I': [],
            'L_O': [],
            'pehe': [],
            'ate_error': []
        }
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=getattr(logging, self.config.log_level),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(self.config.output_dir, 'logs', 'stage1_training.log')),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    @tf.function
    def train_joint_adversarial_step(self, x, t, y, batch_indices):
        """联合对抗训练：同时训练生成器+表征网络和判别器（解决训练不稳定问题）"""

        # 关键：使用两个独立的GradientTape来实现真正的对抗
        with tf.GradientTape() as gen_tape, tf.GradientTape() as disc_tape:
            # 前向传播（一次前向，两个梯度带）
            outputs = self.model([x, t, y], training=True)
            rep_I = outputs['rep_I']
            rep_C = outputs['rep_C']
            rep_A = outputs['rep_A']
            y_logits = outputs['y_logits']
            d_logit = outputs['d_logit']

            # 获取当前批次的样本权重（完全按照DeR-CFR原版）
            current_weights = self.model.get_sample_weights(batch_indices)

            # === DeR-CFR核心损失（只影响表征网络） ===
            L_A = self.model.compute_adjustment_loss(rep_A, t, y, weights=current_weights)
            L_I = self.model.compute_instrumental_loss(rep_I, t, y, weights=current_weights)
            L_O = self.model.compute_orthogonal_loss()
            decomposition_loss = (
                self.config.p_alpha * L_A +
                self.config.p_beta * L_I +
                self.config.p_mu * L_O
            )

            # === 事实监督损失（表征+生成器） ===
            factual_loss = self.model.compute_factual_loss(y_logits, y, t, weights=current_weights)

            # === 对抗损失 ===
            # 判别器损失：正确识别treatment
            d_loss = tf.reduce_mean(
                tf.nn.sigmoid_cross_entropy_with_logits(labels=t, logits=d_logit)
            )

            # 生成器对抗损失：欺骗判别器（融入事实损失，而非额外项）
            g_adv_loss = tf.reduce_mean(
                tf.nn.sigmoid_cross_entropy_with_logits(labels=1-t, logits=d_logit)
            )

            # === 正则化损失 ===
            # 生成器+表征网络的L2正则
            gen_vars = [v for v in self.model.trainable_variables
                       if not (v.name.startswith('stage1_discriminator/') or
                              v.name.endswith('sample_weights:0') or
                              'moving_mean' in v.name or 'moving_variance' in v.name)]
            gen_l2_reg = tf.constant(0.0)
            if gen_vars:
                gen_l2_reg = self.config.p_lambda * tf.add_n([tf.nn.l2_loss(v) for v in gen_vars])

            # 判别器的L2正则
            disc_vars = [v for v in self.model.trainable_variables
                        if v.name.startswith('stage1_discriminator/')]
            disc_l2_reg = tf.constant(0.0)
            if disc_vars:
                disc_l2_reg = self.config.p_lambda * tf.add_n([tf.nn.l2_loss(v) for v in disc_vars])

            # === 组合损失（解决损失项冲突问题） ===
            # 生成器+表征网络的总损失：将对抗损失融入事实损失
            enhanced_factual_loss = factual_loss + self.config.p_adv * g_adv_loss
            generator_total_loss = decomposition_loss + enhanced_factual_loss + gen_l2_reg

            # 判别器的总损失
            discriminator_total_loss = d_loss + disc_l2_reg

        # === 分别更新生成器和判别器（解决次优收敛问题） ===
        # 更新生成器+表征网络（允许梯度自然流动）
        gen_grads = gen_tape.gradient(generator_total_loss, gen_vars)
        self.optimizer_main.apply_gradients(zip(gen_grads, gen_vars))

        # 更新判别器
        disc_grads = disc_tape.gradient(discriminator_total_loss, disc_vars)
        self.optimizer_disc.apply_gradients(zip(disc_grads, disc_vars))

        return {
            'generator_loss': generator_total_loss,
            'discriminator_loss': discriminator_total_loss,
            'decomposition_loss': decomposition_loss,
            'factual_loss': factual_loss,
            'adversarial_loss': g_adv_loss,
            'enhanced_factual_loss': enhanced_factual_loss,
            'L_A': L_A,
            'L_I': L_I,
            'L_O': L_O
        }
    
    # 原train_main_step方法已被train_joint_adversarial_step替代
    
    @tf.function
    def train_weights_step(self, x, t, y, batch_indices):
        """训练样本权重的一步（如果启用）"""
        if not self.config.reweight_sample:
            return tf.constant(0.0)

        with tf.GradientTape() as tape:
            # 前向传播获取混淆表征
            outputs = self.model([x, t, y], training=True)
            rep_C = outputs['rep_C']

            # 获取当前样本权重（按照DeR-CFR原版）
            current_weights = self.model.get_sample_weights(batch_indices)

            # 计算混淆变量平衡损失（使用当前权重）
            balance_loss = self.model.compute_ipm_loss(rep_C, t, weights=current_weights)

            # 权重正则化（完全按照DeR-CFR原版：按治疗臂分离）
            # 分离治疗组和对照组的权重
            i0 = tf.where(t < 0.5)[:, 0]  # 对照组索引
            i1 = tf.where(t >= 0.5)[:, 0]  # 治疗组索引

            weights_0 = tf.gather(current_weights, i0)  # 对照组权重
            weights_1 = tf.gather(current_weights, i1)  # 治疗组权重

            # 按照DeR-CFR原版：(Σω₀-1)²+(Σω₁-1)²
            weight_reg_0 = tf.square(tf.reduce_sum(weights_0) / (tf.cast(tf.shape(weights_0)[0], tf.float32) + 1e-8) - 1.0)
            weight_reg_1 = tf.square(tf.reduce_sum(weights_1) / (tf.cast(tf.shape(weights_1)[0], tf.float32) + 1e-8) - 1.0)
            weight_reg = weight_reg_0 + weight_reg_1

            total_loss = balance_loss + self.config.p_gamma * weight_reg


        # 更新样本权重
        grads = tape.gradient(total_loss, [self.model.sample_weights])
        if grads[0] is not None:
            self.optimizer_weights.apply_gradients(
                zip(grads, [self.model.sample_weights])
            )

        return total_loss
    
    def train_epoch_derc_style(self, batch_generator, steps_per_epoch):
        """
        使用DeR-CFR风格的批次生成器训练一个epoch

        Args:
            batch_generator: DeR-CFR风格的批次生成器
            steps_per_epoch: 每个epoch的步数
        """
        epoch_losses = {
            'generator_loss': 0,
            'discriminator_loss': 0,
            'decomposition_loss': 0,
            'adversarial_loss': 0,
            'factual_loss': 0,
            'enhanced_factual_loss': 0,
            'balance_loss': 0,
            'L_A': 0,
            'L_I': 0,
            'L_O': 0
        }

        for step in range(steps_per_epoch):
            # 使用DeR-CFR风格获取批次数据
            x, t, y, I, ycf, mu0, mu1 = batch_generator.batch.__next__()

            # 转换为tensor
            batch_x = tf.cast(x, tf.float32)
            batch_t = tf.cast(t, tf.float32)
            batch_y = tf.cast(y, tf.float32)
            batch_indices = tf.cast(I, tf.int32)  # 样本索引

            # === 步骤1：联合对抗训练（生成器+表征网络 vs 判别器） ===
            adversarial_losses = self.train_joint_adversarial_step(batch_x, batch_t, batch_y, batch_indices)

            # === 步骤2：样本权重更新（保持DeR-CFR的核心机制） ===
            balance_loss = self.train_weights_step(batch_x, batch_t, batch_y, batch_indices)

            # 累积损失
            epoch_losses['generator_loss'] += adversarial_losses['generator_loss']
            epoch_losses['discriminator_loss'] += adversarial_losses['discriminator_loss']
            epoch_losses['decomposition_loss'] += adversarial_losses['decomposition_loss']
            epoch_losses['adversarial_loss'] += adversarial_losses['adversarial_loss']
            epoch_losses['factual_loss'] += adversarial_losses['factual_loss']
            epoch_losses['enhanced_factual_loss'] += adversarial_losses['enhanced_factual_loss']
            epoch_losses['balance_loss'] += balance_loss
            epoch_losses['L_A'] += adversarial_losses['L_A']
            epoch_losses['L_I'] += adversarial_losses['L_I']
            epoch_losses['L_O'] += adversarial_losses['L_O']

        # 平均损失
        for key in epoch_losses:
            epoch_losses[key] /= steps_per_epoch

        return epoch_losses
    
    def train(self, data_loader, test_data):
        """
        训练阶段一模型（使用DeR-CFR风格的批次生成器）

        Args:
            data_loader: NPZ数据加载器
            test_data: (test_x, test_t, test_y, test_potential_y)
        """
        test_x, test_t, test_y, test_potential_y = test_data

        # 确保数据类型正确
        test_x = np.array(test_x, dtype=np.float32)
        test_t = np.array(test_t, dtype=np.float32)
        test_y = np.array(test_y, dtype=np.float32)
        test_potential_y = np.array(test_potential_y, dtype=np.float32)

        # 创建DeR-CFR风格的批次生成器
        batch_generator = create_derc_batch_generator(data_loader, self.config.batch_size)

        # 计算每个epoch的步数（类似DeR-CFR的train_steps）
        steps_per_epoch = batch_generator.batch_num

        # 修复：初始化样本权重以支持全局索引
        total_samples = data_loader.num  # 使用总样本数（训练+验证+测试）
        train_samples = data_loader.train_I
        self.model.build_sample_weights(total_samples)

        print(f"Sample weights initialization (FIXED):")
        print(f"  Total samples: {total_samples}")
        print(f"  Training samples: {train_samples}")
        print(f"  Global index support: 0 ~ {total_samples-1}")

        self.logger.info("Starting Stage 1 training (DeR-CFR style)...")
        self.logger.info(f"Training samples: {train_samples}")
        self.logger.info(f"Test samples: {len(test_x)}")
        self.logger.info(f"Batch size: {self.config.batch_size}")
        self.logger.info(f"Steps per epoch: {steps_per_epoch}")
        self.logger.info(f"Sample weights initialized: shape={self.model.sample_weights.shape}")

        best_pehe = float('inf')
        patience_counter = 0

        for epoch in range(self.config.stage1_epochs):
            # 使用DeR-CFR风格训练一个epoch
            epoch_losses = self.train_epoch_derc_style(batch_generator, steps_per_epoch)

            # 记录损失
            for key, value in epoch_losses.items():
                if key in self.train_history:
                    self.train_history[key].append(float(value))

            # 定期评估
            if epoch % self.config.eval_freq == 0:
                # 评估模型
                eval_results = evaluate_model(
                    self.model, test_x, test_t, test_y, test_potential_y, stage='stage1'
                )

                # 记录评估结果
                self.train_history['pehe'].append(eval_results['pehe'])
                self.train_history['ate_error'].append(eval_results['ate_error'])

                # 打印结果
                self.logger.info(f"Epoch {epoch}:")
                self.logger.info(f"  Generator Loss: {epoch_losses['generator_loss']:.6f}")
                self.logger.info(f"  Discriminator Loss: {epoch_losses['discriminator_loss']:.6f}")
                self.logger.info(f"  PEHE: {eval_results['pehe']:.6f}")
                self.logger.info(f"  ATE Error: {eval_results['ate_error']:.6f}")

                # 详细损失分解
                self.logger.info(f"    - DeR-CFR Losses: L_A={epoch_losses['L_A']:.4f}, L_I={epoch_losses['L_I']:.4f}, L_O={epoch_losses['L_O']:.4f}")
                self.logger.info(f"    - Factual Loss: {epoch_losses['factual_loss']:.4f} -> Enhanced: {epoch_losses['enhanced_factual_loss']:.4f}")
                self.logger.info(f"    - Adversarial Loss: {epoch_losses['adversarial_loss']:.4f}")
                self.logger.info(f"    - Balance Loss (Weights): {epoch_losses['balance_loss']:.4f}")

                # 早停检查
                if eval_results['pehe'] < best_pehe:
                    best_pehe = eval_results['pehe']
                    patience_counter = 0

                    # 保存最佳模型
                    self.save_model('best_stage1_model')
                else:
                    patience_counter += 1

                if patience_counter >= self.config.early_stop_patience:
                    self.logger.info(f"Early stopping at epoch {epoch}")
                    break

            # 定期保存
            if epoch % self.config.save_freq == 0:
                self.save_model(f'stage1_model_epoch_{epoch}')

        self.logger.info("Stage 1 training completed!")
        return self.train_history
    
    def save_model(self, model_name):
        """保存模型"""
        save_path = os.path.join(self.config.output_dir, 'models', model_name)
        self.model.save_weights(save_path)
        self.logger.info(f"Model saved to {save_path}")
    
    def load_model(self, model_path):
        """加载模型"""
        self.model.load_weights(model_path)
        self.logger.info(f"Model loaded from {model_path}")
    
    def generate_stage1_outputs(self, x, t, y):
        """生成阶段一的输出（用于阶段二）"""
        outputs = self.model([x, t, y], training=False)
        return outputs['y_logits']
