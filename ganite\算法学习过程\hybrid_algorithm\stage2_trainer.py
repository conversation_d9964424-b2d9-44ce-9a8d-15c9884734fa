#!/usr/bin/env python3
"""
阶段二训练器：ITE估计精炼器
完全按照VGANITE的Stage2训练流程
"""

import tensorflow as tf
import numpy as np
import os
import logging

from models.stage2_model import Stage2Model, gradient_penalty
from data_loader import batch_generator
from evaluation import evaluate_model, evaluate_mode_collapse_indicators, print_mode_collapse_indicators

class Stage2Trainer:
    """阶段二训练器：ITE估计精炼器"""
    
    def __init__(self, config, stage1_model):
        self.config = config
        self.stage1_model = stage1_model
        
        # 创建阶段二模型
        self.model = Stage2Model(config)
        
        # 优化器
        self.optimizer_gen = tf.keras.optimizers.Adam(
            learning_rate=config.stage2_lr, beta_1=0.5, beta_2=0.9
        )
        self.optimizer_critic = tf.keras.optimizers.Adam(
            learning_rate=config.stage2_lr, beta_1=0.5, beta_2=0.9
        )
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
        # 训练历史
        self.history = {
            'gen_loss': [],
            'critic_loss': [],
            'wass_dist': [],  # 添加缺失的键
            'gp_loss': [],
            'adversarial_loss': [],  # 添加缺失的键
            'supervised_loss': [],
            'pehe': [],
            'ate_error': []
        }
    
    @tf.function
    def train_critic_step(self, x_batch, y_bar_batch_real):
        """训练评论家的一步（完全按照VGANITE的WGAN-GP实现）"""
        with tf.GradientTape() as tape_c:
            # 生成器生成假的潜在结果（完全按照VGANITE）
            y_tilde_logits = self.model.generator(x_batch, training=True)
            y_tilde_prob = tf.nn.sigmoid(y_tilde_logits)

            # 评论家评分（完全按照VGANITE）
            c_real = self.model.critic(x_batch, y_bar_batch_real)
            c_fake = self.model.critic(x_batch, y_tilde_prob)

            # WGAN损失（完全按照VGANITE）
            wass_dist = tf.reduce_mean(c_fake) - tf.reduce_mean(c_real)

            # 梯度惩罚（完全按照VGANITE）
            gp = gradient_penalty(self.model.critic, x_batch, y_bar_batch_real, y_tilde_prob)

            # 总损失（完全按照VGANITE）
            c_loss = wass_dist + self.config.lambda_gp * gp

        # 更新评论家（完全按照VGANITE，包括梯度裁剪）
        c_grads = tape_c.gradient(c_loss, self.model.critic.trainable_variables)
        c_grads_clipped, _ = tf.clip_by_global_norm(c_grads, 5.0)  # VGANITE使用的梯度裁剪

        # 检查梯度是否存在（完全按照VGANITE）
        if all(g is not None for g in c_grads_clipped):
            self.optimizer_critic.apply_gradients(zip(c_grads_clipped, self.model.critic.trainable_variables))

        return c_loss, wass_dist, gp
    
    @tf.function
    def train_generator_step(self, x_batch, y_bar_batch_real):
        """训练生成器的一步（完全按照VGANITE的实现）"""
        with tf.GradientTape() as tape_g:
            # 生成器生成假的潜在结果（完全按照VGANITE）
            y_tilde_logits = self.model.generator(x_batch, training=True)
            y_tilde_prob = tf.nn.sigmoid(y_tilde_logits)

            # 对抗损失（完全按照VGANITE）
            c_fake_for_g = self.model.critic(x_batch, y_tilde_prob)
            g_loss_adversarial = -tf.reduce_mean(c_fake_for_g)

            # 监督损失（完全按照VGANITE，使用L1损失）
            g_loss_supervised = tf.reduce_mean(tf.abs(y_tilde_prob - y_bar_batch_real))

            # 总损失（完全按照VGANITE）
            g_loss = g_loss_adversarial + self.config.lambda_supervised * g_loss_supervised

        # 更新生成器（完全按照VGANITE，包括梯度裁剪）
        g_grads = tape_g.gradient(g_loss, self.model.generator.trainable_variables)
        g_grads_clipped, _ = tf.clip_by_global_norm(g_grads, 5.0)  # VGANITE使用的梯度裁剪

        # 检查梯度是否存在（完全按照VGANITE）
        if all(g is not None for g in g_grads_clipped):
            self.optimizer_gen.apply_gradients(zip(g_grads_clipped, self.model.generator.trainable_variables))

        return g_loss, g_loss_adversarial, g_loss_supervised
    
    def train_epoch(self, train_x, y_bar_target):
        """
        训练一个epoch（完全按照VGANITE的Stage2训练流程）

        Args:
            train_x: 原始特征 [n_samples, x_dim]
            y_bar_target: 阶段一的输出作为目标 [n_samples, 2]
        """
        epoch_losses = {
            'gen_loss': 0.0,
            'critic_loss': 0.0,
            'wass_dist': 0.0,
            'gp_loss': 0.0,
            'adversarial_loss': 0.0,
            'supervised_loss': 0.0
        }

        # 创建数据集（完全按照VGANITE的实现）
        stage2_dataset = tf.data.Dataset.from_tensor_slices((train_x, y_bar_target)).shuffle(
            buffer_size=len(train_x)).batch(self.config.batch_size).prefetch(tf.data.AUTOTUNE)

        num_batches = 0

        # 批次训练（完全按照VGANITE的训练循环）
        for x_batch, y_bar_batch_real in stage2_dataset:
            # 训练评论家（多次，完全按照VGANITE）
            for _ in range(self.config.n_critic):
                critic_loss, wass_dist, gp_loss = self.train_critic_step(x_batch, y_bar_batch_real)
                epoch_losses['critic_loss'] += critic_loss
                epoch_losses['wass_dist'] += wass_dist
                epoch_losses['gp_loss'] += gp_loss

            # 训练生成器（一次，完全按照VGANITE）
            gen_loss, adversarial_loss, supervised_loss = self.train_generator_step(x_batch, y_bar_batch_real)
            epoch_losses['gen_loss'] += gen_loss
            epoch_losses['adversarial_loss'] += adversarial_loss
            epoch_losses['supervised_loss'] += supervised_loss

            num_batches += 1

        # 计算平均损失
        for key in epoch_losses:
            if key in ['critic_loss', 'wass_dist', 'gp_loss']:
                epoch_losses[key] /= (num_batches * self.config.n_critic)
            else:
                epoch_losses[key] /= num_batches

        return epoch_losses
    
    def train(self, train_data, test_data):
        """训练阶段二模型"""
        train_x, train_t, train_y, train_potential_y = train_data
        test_x, test_t, test_y, test_potential_y = test_data

        self.logger.info("Starting Stage 2 training...")
        self.logger.info(f"Training samples: {len(train_x) if train_x is not None else 'N/A'}")
        self.logger.info(f"Test samples: {len(test_x) if test_x is not None else 'N/A'}")

        # 检查是否为快速测试模式（数据为None）
        if train_t is None or train_y is None:
            self.logger.info("Stage 2 training skipped for quick test")
            return {'gen_loss': [], 'critic_loss': [], 'gp_loss': [], 'supervised_loss': []}

        # 生成阶段一的输出作为阶段二的目标（完全按照VGANITE）
        self.logger.info("Generating Stage 1 outputs for Stage 2 training...")
        stage1_outputs = self.stage1_model([train_x, train_t, train_y], training=False)
        y_bar_logits_from_stage1 = stage1_outputs['y_logits']
        # 使用概率作为目标分布（与VGANITE完全一致）
        y_bar_prob_target = tf.nn.sigmoid(y_bar_logits_from_stage1)

        # 完整训练模式（完全按照VGANITE的迭代设置）
        self.logger.info(f"Starting Stage 2 training for {self.config.stage2_epochs} iterations...")

        # 创建数据集（完全按照VGANITE的wgangp.py第469-470行）
        x_train_tf = tf.cast(train_x, tf.float32)
        stage2_dataset = tf.data.Dataset.from_tensor_slices((x_train_tf, y_bar_prob_target)).shuffle(
            buffer_size=len(train_x)).batch(self.config.batch_size).prefetch(tf.data.AUTOTUNE)

        # 创建tf.function训练步骤（完全按照VGANITE的wgangp.py第473-514行）
        @tf.function
        def stage2_train_step(x_batch, y_bar_batch_real):
            # 训练ITE评论家（WGAN-GP）
            for _ in range(self.config.n_critic):
                with tf.GradientTape() as tape_c:
                    y_tilde_logits = self.model.generator(x_batch, training=True)
                    y_tilde_prob = tf.nn.sigmoid(y_tilde_logits)
                    c_real = self.model.critic(x_batch, y_bar_batch_real)
                    c_fake = self.model.critic(x_batch, y_tilde_prob)
                    wass_dist = tf.reduce_mean(c_fake) - tf.reduce_mean(c_real)
                    gp = gradient_penalty(self.model.critic, x_batch, y_bar_batch_real, y_tilde_prob)
                    c_loss = wass_dist + self.config.lambda_gp * gp

                c_grads = tape_c.gradient(c_loss, self.model.critic.trainable_variables)
                # 应用梯度裁剪（完全按照VGANITE）
                c_grads_clipped, _ = tf.clip_by_global_norm(c_grads, 5.0)
                # 检查梯度是否存在
                if all(g is not None for g in c_grads_clipped):
                    self.optimizer_critic.apply_gradients(zip(c_grads_clipped, self.model.critic.trainable_variables))

            # 训练ITE生成器（WGAN-GP）
            with tf.GradientTape() as tape_g:
                y_tilde_logits = self.model.generator(x_batch, training=True)
                y_tilde_prob = tf.nn.sigmoid(y_tilde_logits)
                c_fake_for_g = self.model.critic(x_batch, y_tilde_prob)
                g_loss_adversarial = -tf.reduce_mean(c_fake_for_g)
                g_loss_supervised = tf.reduce_mean(tf.abs(y_tilde_prob - y_bar_batch_real))
                g_loss = g_loss_adversarial + self.config.lambda_supervised * g_loss_supervised

            g_grads = tape_g.gradient(g_loss, self.model.generator.trainable_variables)
            # 应用梯度裁剪（完全按照VGANITE）
            g_grads_clipped, _ = tf.clip_by_global_norm(g_grads, 5.0)
            # 检查梯度是否存在
            if all(g is not None for g in g_grads_clipped):
                self.optimizer_gen.apply_gradients(zip(g_grads_clipped, self.model.generator.trainable_variables))

            return c_loss, wass_dist, gp, g_loss, g_loss_adversarial, g_loss_supervised

        # 训练循环（完全按照VGANITE的wgangp.py第517-526行）
        for iteration in range(self.config.stage2_epochs):
            for x_batch, y_bar_batch_real in stage2_dataset:
                # 运行训练步骤
                c_loss, wass_dist, gp, g_loss, g_loss_adversarial, g_loss_supervised = stage2_train_step(x_batch, y_bar_batch_real)

            # 记录损失
            self.history['gen_loss'].append(float(g_loss))
            self.history['critic_loss'].append(float(c_loss))
            self.history['wass_dist'].append(float(wass_dist))
            self.history['gp_loss'].append(float(gp))
            self.history['adversarial_loss'].append(float(g_loss_adversarial))
            self.history['supervised_loss'].append(float(g_loss_supervised))

            # 定期评估和打印（完全按照VGANITE）
            if iteration % 100 == 0 or iteration == self.config.stage2_epochs - 1:
                self.logger.info(
                    f'Stage 2 - Iter: {iteration}/{self.config.stage2_epochs}, '
                    f'ITE_C Loss: {c_loss.numpy():.4f} (Wass: {wass_dist.numpy():.4f}, GP: {gp.numpy():.4f}), '
                    f'ITE_G Loss: {g_loss.numpy():.4f} (Adv: {g_loss_adversarial.numpy():.4f}, Sup: {g_loss_supervised.numpy():.4f})'
                )

        self.logger.info("Stage 2 training completed!")
        return self.history

    def train_with_stage1_outputs(self, stage1_outputs, test_data=None):
        """
        使用阶段一的输出进行阶段二训练
        完全照搬VGANITE的wgangp.py第469-526行，只改变变量名对接

        Args:
            stage1_outputs: 阶段一的输出
            test_data: 测试数据（用于模式崩塌监控）
        """
        self.logger.info("Starting Stage 2 training with Stage 1 outputs...")

        # 获取阶段一的输出数据（对应wgangp.py第461-466行）
        x_train_tf = stage1_outputs['train_x']
        y_bar_prob_target = stage1_outputs['train_y_bar_prob']

        self.logger.info(f"Stage 2 training data shapes:")
        self.logger.info(f"  x_train: {x_train_tf.shape}")
        self.logger.info(f"  y_bar_target: {y_bar_prob_target.shape}")

        # 创建数据集（完全照搬wgangp.py第469-470行）
        stage2_dataset = tf.data.Dataset.from_tensor_slices((x_train_tf, y_bar_prob_target)).shuffle(
            buffer_size=len(x_train_tf)).batch(self.config.batch_size).prefetch(tf.data.AUTOTUNE)

        # 使用tf.function进行潜在的加速和图优化（完全照搬wgangp.py第473行）
        @tf.function
        def stage2_train_step(x_batch, y_bar_batch_real):
            # --- 训练ITE评论家（WGAN-GP）---（完全照搬wgangp.py第475-494行）
            for _ in range(self.config.n_critic):
                with tf.GradientTape() as tape_c:
                    y_tilde_logits = self.model.generator(x_batch, training=True)  # 对应ITE_G
                    y_tilde_prob = tf.nn.sigmoid(y_tilde_logits)
                    c_real = self.model.critic(x_batch, y_bar_batch_real)  # 对应ITE_C
                    c_fake = self.model.critic(x_batch, y_tilde_prob)
                    wass_dist = tf.reduce_mean(c_fake) - tf.reduce_mean(c_real)
                    gp = gradient_penalty(self.model.critic, x_batch, y_bar_batch_real, y_tilde_prob)
                    c_loss = wass_dist + self.config.lambda_gp * gp

                c_grads = tape_c.gradient(c_loss, self.model.critic.trainable_variables)
                # 应用评论家的梯度裁剪（完全照搬wgangp.py第488行）
                c_grads_clipped, _ = tf.clip_by_global_norm(c_grads, 5.0)  # gradient_clip_norm
                # 在应用前检查梯度是否存在（完全照搬wgangp.py第490-491行）
                if all(g is not None for g in c_grads_clipped):
                    self.optimizer_critic.apply_gradients(zip(c_grads_clipped, self.model.critic.trainable_variables))

            # --- 训练ITE生成器（WGAN-GP）---（完全照搬wgangp.py第496-513行）
            with tf.GradientTape() as tape_g:
                y_tilde_logits = self.model.generator(x_batch, training=True)  # 对应ITE_G
                y_tilde_prob = tf.nn.sigmoid(y_tilde_logits)
                c_fake_for_g = self.model.critic(x_batch, y_tilde_prob)  # 对应ITE_C
                g_loss_adversarial = -tf.reduce_mean(c_fake_for_g)
                g_loss_supervised = tf.reduce_mean(tf.abs(y_tilde_prob - y_bar_batch_real))
                g_loss = g_loss_adversarial + self.config.lambda_supervised * g_loss_supervised  # 对应lambda_I_supervised

            g_grads = tape_g.gradient(g_loss, self.model.generator.trainable_variables)
            # 应用生成器的梯度裁剪（完全照搬wgangp.py第507行）
            g_grads_clipped, _ = tf.clip_by_global_norm(g_grads, 5.0)  # gradient_clip_norm
            # 在应用前检查梯度是否存在（完全照搬wgangp.py第509-510行）
            if all(g is not None for g in g_grads_clipped):
                self.optimizer_gen.apply_gradients(zip(g_grads_clipped, self.model.generator.trainable_variables))

            return c_loss, wass_dist, gp, g_loss, g_loss_adversarial, g_loss_supervised

        # --- 阶段二训练循环 ---（完全照搬wgangp.py第517-520行）
        for it in range(self.config.stage2_epochs):  # 对应iterations
            for x_batch, y_bar_batch_real in stage2_dataset:
                # 运行训练步骤
                c_loss, wass_dist, gp, g_loss, g_loss_adversarial, g_loss_supervised = stage2_train_step(x_batch, y_bar_batch_real)

            # --- 日志记录（阶段二）---（修正：使用logger保持一致性）
            if it % 100 == 0 or it == self.config.stage2_epochs - 1:
                # 使用logger.info而不是print，确保输出一致性
                self.logger.info(f'Stage 2 - Iter: {it}/{self.config.stage2_epochs}, ITE_C Loss: {c_loss.numpy():.4f} (Wass: {wass_dist.numpy():.4f}, GP: {gp.numpy():.4f}), ITE_G Loss: {g_loss.numpy():.4f} (Adv: {g_loss_adversarial.numpy():.4f}, Sup: {g_loss_supervised.numpy():.4f})')

                # 同时使用print确保在控制台立即显示
                print(f'Stage 2 - Iter: {it}/{self.config.stage2_epochs}, ITE_C Loss: {c_loss.numpy():.4f} (Wass: {wass_dist.numpy():.4f}, GP: {gp.numpy():.4f}), ITE_G Loss: {g_loss.numpy():.4f} (Adv: {g_loss_adversarial.numpy():.4f}, Sup: {g_loss_supervised.numpy():.4f})')

                # 模式崩塌监控（如果有测试数据）
                if test_data is not None:
                    test_x, test_t, test_y, test_potential_y = test_data
                    stage1_y_bar_prob = stage1_outputs['test_y_bar_prob']

                    # 评估模式崩塌指标
                    mode_collapse_indicators = evaluate_mode_collapse_indicators(
                        self.model, test_x, test_t, test_y, test_potential_y,
                        stage='stage2', stage1_y_bar_prob=stage1_y_bar_prob
                    )

                    # 打印模式崩塌监控指标
                    print_mode_collapse_indicators(mode_collapse_indicators, epoch=it)

                # 强制刷新输出缓冲区
                import sys
                sys.stdout.flush()

            # 记录损失到历史
            self.history['gen_loss'].append(float(g_loss))
            self.history['critic_loss'].append(float(c_loss))
            self.history['wass_dist'].append(float(wass_dist))
            self.history['gp_loss'].append(float(gp))
            self.history['adversarial_loss'].append(float(g_loss_adversarial))
            self.history['supervised_loss'].append(float(g_loss_supervised))

        self.logger.info("Stage 2 training completed!")
        return self.history
    
    def save_model(self, save_path_or_epoch):
        """保存模型（支持两种模式）"""
        if isinstance(save_path_or_epoch, int):
            # 完整训练模式：按epoch保存
            epoch = save_path_or_epoch
            save_dir = os.path.join(self.config.output_dir, 'stage2_models')
            os.makedirs(save_dir, exist_ok=True)

            # 保存生成器
            gen_path = os.path.join(save_dir, f'generator_epoch_{epoch}.h5')
            self.model.generator.save_weights(gen_path)

            # 保存评论家
            critic_path = os.path.join(save_dir, f'critic_epoch_{epoch}.h5')
            self.model.critic.save_weights(critic_path)

            self.logger.info(f"Stage2 model saved at epoch {epoch}")
        else:
            # 快速测试模式：按路径保存（但实际跳过）
            save_path = save_path_or_epoch
            os.makedirs(save_path, exist_ok=True)
            self.logger.info(f"Stage2 model save skipped for quick test")

    def load_model(self, save_path_or_epoch):
        """加载模型（支持两种模式）"""
        if isinstance(save_path_or_epoch, int):
            # 完整训练模式：按epoch加载
            epoch = save_path_or_epoch
            save_dir = os.path.join(self.config.output_dir, 'stage2_models')

            # 加载生成器
            gen_path = os.path.join(save_dir, f'generator_epoch_{epoch}.h5')
            if os.path.exists(gen_path):
                self.model.generator.load_weights(gen_path)

            # 加载评论家
            critic_path = os.path.join(save_dir, f'critic_epoch_{epoch}.h5')
            if os.path.exists(critic_path):
                self.model.critic.load_weights(critic_path)

            self.logger.info(f"Stage2 model loaded from epoch {epoch}")
        else:
            # 快速测试模式：跳过加载
            self.logger.info(f"Stage2 model load skipped for quick test")
