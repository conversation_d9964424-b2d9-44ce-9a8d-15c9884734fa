"""
NPZ数据加载模块：使用DeR-CFR的数据格式和划分方式
借鉴DeR-CFR/utils.py中的load_npz函数，读取data28/Twins38.combined.npz数据集
"""

import numpy as np
import tensorflow as tf
import os
import math

class Load_NPZ_Data(object):
    """
    NPZ数据加载器，完全按照DeR-CFR的方式
    借鉴DeR-CFR/utils.py中的Load_Data类
    """
    
    def __init__(self, train_valid_test=[63, 27, 10], seed=123):
        """
        初始化数据加载器（完全按照DeR-CFR的方式）

        Args:
            train_valid_test: 训练/验证/测试集比例 [63, 27, 10] = [63%, 27%, 10%]
            seed: 随机种子
        """
        self.data = None
        self.num = 0
        self.rng = np.random.RandomState(seed)
        self.train_valid_test = train_valid_test
        
    def reinit(self):
        """重新初始化"""
        self.data = None
        self.num = 0
    
    def load_npz(self, file_path, ind=0):
        """
        加载NPZ文件，完全按照DeR-CFR的load_npz函数
        
        Args:
            file_path: NPZ文件路径
            ind: 数据索引（用于多重复实验）
        """
        print(f"Loading NPZ data from: {file_path}")

        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"NPZ file not found: {file_path}")
        
        # 加载NPZ数据
        data = np.load(file_path)
        
        print(f"NPZ file keys: {list(data.keys())}")
        
        # 按照DeR-CFR的格式读取变量
        t = data['t'][:, ind:ind+1]           # 处理变量
        y = data['yf'][:, ind:ind+1]          # 事实结果
        ycf = data['ycf'][:, ind:ind+1]       # 反事实结果
        mu0 = data['mu0'][:, ind:ind+1]       # 潜在结果Y(0)
        mu1 = data['mu1'][:, ind:ind+1]       # 潜在结果Y(1)
        x = data['x'][:, :, ind]              # 协变量特征
        
        self.num = self.num + x.shape[0]
        
        # 按照DeR-CFR的格式组织数据
        data_list = [x, t, y, ycf, mu0, mu1]
        
        if self.data == None:
            self.data = data_list
        else:
            for i, _ in enumerate(self.data):
                self.data[i] = np.concatenate((self.data[i], data_list[i]), axis=0)
        
        print(f"Data loaded successfully:")
        print(f"  Samples: {x.shape[0]}")
        print(f"  Features: {x.shape[1]}")
        print(f"  Treatment ratio: {np.mean(t):.3f}")
        print(f"  Outcome ratio: {np.mean(y):.3f}")
    
    def split_data(self, train_valid_test=None):
        """
        划分训练/验证/测试集，完全按照DeR-CFR的方式
        
        Args:
            train_valid_test: 自定义划分比例
        """
        if train_valid_test == None:
            train_valid_test = (self.num * np.array(self.train_valid_test) / sum(self.train_valid_test)).astype(int)
        else:
            self.train_valid_test = train_valid_test
            train_valid_test = (self.num * np.array(train_valid_test) / sum(train_valid_test)).astype(int)
        
        # 按照DeR-CFR的方式划分数据
        self.train = [d[0:train_valid_test[0], :] for d in self.data]
        self.valid = [d[train_valid_test[0]:int(sum(train_valid_test[0:2])), :] for d in self.data]
        self.test = [d[int(sum(train_valid_test[0:2])):, :] for d in self.data]
        
        self.train_I, self.valid_I, self.test_I = train_valid_test
        
        # 转换为字典格式
        self.to_dict()
        
        print(f"Data split completed:")
        print(f"  Training samples: {self.train_I}")
        print(f"  Validation samples: {self.valid_I}")
        print(f"  Test samples: {self.test_I}")
    
    def to_dict(self):
        """转换为字典格式，完全按照DeR-CFR的方式"""
        self.train = self.list_2_dict(self.train)
        self.valid = self.list_2_dict(self.valid)
        self.test = self.list_2_dict(self.test)

        # 修复索引：使用全局唯一索引，避免重叠
        # 注意：即使数据被随机打乱，索引仍然按照划分后的位置分配
        self.train['I'] = np.array(range(0, self.train_I))                                    # 0~3319
        self.valid['I'] = np.array(range(self.train_I, self.train_I + self.valid_I))         # 3320~4742
        self.test['I'] = np.array(range(self.train_I + self.valid_I, self.num))              # 4743~5270

        print(f"Random split with global unique indices:")
        print(f"  Train indices: {self.train['I'][0]} ~ {self.train['I'][-1]} (total: {len(self.train['I'])})")
        print(f"  Valid indices: {self.valid['I'][0]} ~ {self.valid['I'][-1]} (total: {len(self.valid['I'])})")
        print(f"  Test indices: {self.test['I'][0]} ~ {self.test['I'][-1]} (total: {len(self.test['I'])})")
        print(f"  数据已随机打乱，各数据集样本分布相似")
    
    def list_2_dict(self, data_list):
        """
        将列表转换为字典，完全按照DeR-CFR的格式
        
        Args:
            data_list: [x, t, y, ycf, mu0, mu1]
            
        Returns:
            data_dict: 包含所有变量的字典
        """
        data_dict = {}
        data_dict['x'] = data_list[0]      # 协变量特征
        data_dict['t'] = data_list[1]      # 处理变量
        data_dict['y'] = data_list[2]      # 事实结果
        data_dict['ycf'] = data_list[3]    # 反事实结果
        data_dict['mu0'] = data_list[4]    # 潜在结果Y(0)
        data_dict['mu1'] = data_list[5]    # 潜在结果Y(1)
        
        return data_dict
    
    def shuffle(self):
        """打乱数据并重新划分，完全按照DeR-CFR的方式"""
        p = self.rng.permutation(self.num)
        self.data = [d[p] for d in self.data]
        self.split_data()


def load_twins_npz_data(data_path='data28/Twins38.combined.npz',
                        train_valid_test=[63, 27, 10],
                        seed=123,
                        ind=0):
    """
    加载Twins NPZ数据的便捷函数（修复：按照原DeR-CFR方式随机划分）

    Args:
        data_path: NPZ文件路径
        train_valid_test: 训练/验证/测试集比例
        seed: 随机种子
        ind: 数据索引

    Returns:
        data_loader: 包含train/valid/test数据的加载器对象
    """
    # 创建数据加载器
    data_loader = Load_NPZ_Data(train_valid_test=train_valid_test, seed=seed)

    # 加载NPZ数据
    data_loader.load_npz(data_path, ind=ind)

    # 修复：按照原DeR-CFR的方式，先随机打乱数据再划分
    # 这样确保训练集、验证集、测试集的样本分布相似
    print("🔧 按照原DeR-CFR方式随机划分数据:")
    print(f"  随机种子: {seed}")
    print("  先随机打乱数据，再按比例划分")
    data_loader.shuffle()  # 这会自动调用split_data()

    return data_loader


def compute_thresholds(data_loader):
    """
    计算阈值，完全按照DeR-CFR的方式

    Args:
        data_loader: 数据加载器对象

    Returns:
        t_threshold: 处理变量阈值 (固定为0.5)
        y_threshold: 结果变量阈值 (训练+验证集的中位数)
    """
    # 处理变量阈值固定为0.5 (完全按照DeR-CFR)
    t_threshold = 0.5

    # 结果变量阈值为训练+验证集的中位数 (完全按照DeR-CFR)
    ys = np.concatenate((data_loader.train['y'], data_loader.valid['y']), axis=0)
    y_threshold = np.median(ys)

    return t_threshold, y_threshold


def get_npz_data_info(data_loader):
    """
    获取NPZ数据集信息
    
    Args:
        data_loader: 数据加载器对象
        
    Returns:
        info: 数据集信息字典
    """
    train_data = data_loader.train
    
    info = {
        'total_samples': data_loader.num,
        'train_samples': data_loader.train_I,
        'valid_samples': data_loader.valid_I,
        'test_samples': data_loader.test_I,
        'x_dim': train_data['x'].shape[1],
        'treatment_ratio': float(np.mean(train_data['t'])),
        'outcome_ratio': float(np.mean(train_data['y'])),
        'x_mean': np.mean(train_data['x'], axis=0),
        'x_std': np.std(train_data['x'], axis=0)
    }
    
    return info


class DeRCFRBatchGenerator(object):
    """
    DeR-CFR风格的批次生成器
    完全按照DeR-CFR/utils.py中的batch_G类实现
    """

    def __init__(self, data, batch_size, shuffle_=True, seed=123):
        """
        初始化批次生成器

        Args:
            data: 数据列表 [x, t, y, ycf, mu0, mu1] 或 [x, t, y]
            batch_size: 批次大小
            shuffle_: 是否打乱数据
            seed: 随机种子
        """
        self.data = data
        if batch_size == 0:
            self.batch_size = self.data[0].shape[0]
        else:
            self.batch_size = batch_size
        self.shuffle_ = shuffle_
        self.rng = np.random.RandomState(seed)
        self.batch = self.batch_generator()
        self.num = self.data[0].shape[0]

        # 修复全批次训练的批次数计算
        if self.batch_size >= self.num:
            # 全批次训练：批次大小大于等于样本数
            self.batch_size = self.num  # 调整为实际样本数
            self.batch_num = 1          # 每个epoch只有1个批次
        else:
            # 正常批次训练
            self.batch_num = math.ceil(self.num / self.batch_size)

        print(f"DeR-CFR Batch Generator initialized:")
        print(f"  Total samples: {self.num}")
        print(f"  Batch size: {self.batch_size}")
        print(f"  Number of batches per epoch: {self.batch_num}")

    def shuffle(self):
        """打乱数据，完全按照DeR-CFR的方式"""
        num = self.data[0].shape[0]
        p = self.rng.permutation(num)
        self.data = [d[p] for d in self.data]

    def batch_generator(self):
        """
        批次生成器，完全按照DeR-CFR的方式
        无限循环生成批次数据
        """
        if self.shuffle_:
            self.shuffle()

        batch_count = 0
        while True:
            if batch_count * self.batch_size + self.batch_size >= len(self.data[0]):
                batch_count = 0
                if self.shuffle_:
                    self.shuffle()

            start = batch_count * self.batch_size
            end = min(start + self.batch_size, len(self.data[0]))  # 防止越界
            batch_count += 1

            yield [d[start:end] for d in self.data]


def create_derc_batch_generator(data_loader, batch_size, include_indices=True):
    """
    为hybrid_algorithm创建DeR-CFR风格的批次生成器

    Args:
        data_loader: NPZ数据加载器
        batch_size: 批次大小
        include_indices: 是否包含样本索引

    Returns:
        batch_generator: DeR-CFR风格的批次生成器
    """
    train_data = data_loader.train

    # 按照DeR-CFR的格式组织数据 [x, t, y, I, ycf, mu0, mu1]
    data_list = [
        train_data['x'],      # 协变量特征
        train_data['t'],      # 处理变量
        train_data['y'],      # 事实结果
        train_data['I'],      # 样本索引
        train_data['ycf'],    # 反事实结果
        train_data['mu0'],    # 潜在结果Y(0)
        train_data['mu1']     # 潜在结果Y(1)
    ]

    # 创建批次生成器（修复：禁用数据打乱以保持训练和评估的数据顺序一致）
    batch_gen = DeRCFRBatchGenerator(
        data=data_list,
        batch_size=batch_size,
        shuffle_=False,  # 修复：禁用打乱，避免样本权重与实际样本不匹配
        seed=123
    )

    print("🔧 数据顺序一致性修复:")
    print("  - 禁用训练时数据打乱")
    print("  - 确保样本权重与实际样本匹配")
    print("  - 训练和评估使用相同数据顺序")

    return batch_gen


if __name__ == "__main__":
    # 测试NPZ数据加载
    print("Testing NPZ data loading...")
    
    try:
        # 加载数据
        data_loader = load_twins_npz_data(
            data_path='data28/Twins38.combined.npz',
            train_valid_test=[7, 2, 1],
            seed=123,
            ind=0
        )
        
        # 获取数据信息
        info = get_npz_data_info(data_loader)
        print("\nData Info:")
        for key, value in info.items():
            print(f"  {key}: {value}")
        
        # 预处理数据
        train_processed = preprocess_npz_data(data_loader.train, normalize_x=True)
        valid_processed = preprocess_npz_data(data_loader.valid, normalize_x=True)
        test_processed = preprocess_npz_data(data_loader.test, normalize_x=True)
        
        print("\nNPZ data loading test completed successfully!")
        
    except Exception as e:
        print(f"Error loading NPZ data: {e}")
