#!/usr/bin/env python3
"""
环境修复脚本：自动检测并修复conda/pip混用问题
"""

import os
import sys
import subprocess
import shutil

def run_command(cmd, check=True):
    """运行命令"""
    print(f"执行: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if result.returncode != 0 and check:
        print(f"错误: {result.stderr}")
        return False
    
    if result.stdout:
        print(result.stdout)
    
    return True

def detect_conda():
    """检测conda是否可用"""
    try:
        result = subprocess.run(['conda', '--version'], capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False

def backup_current_environment():
    """备份当前环境信息"""
    print("备份当前环境信息...")
    
    # 导出pip包列表
    run_command('pip freeze > pip_backup.txt', check=False)
    
    # 如果有conda，导出conda环境
    if detect_conda():
        run_command('conda list --export > conda_backup.txt', check=False)
    
    print("环境信息已备份到 pip_backup.txt 和 conda_backup.txt")

def clean_mixed_packages():
    """清理混用的包"""
    print("清理可能冲突的包...")
    
    # 需要清理的科学计算包
    packages_to_clean = [
        'numpy', 'scipy', 'pandas', 'scikit-learn', 
        'matplotlib', 'seaborn', 'tensorflow'
    ]
    
    for package in packages_to_clean:
        print(f"卸载 {package}...")
        run_command(f'pip uninstall -y {package}', check=False)

def install_safe_environment():
    """安装安全的环境"""
    print("安装安全的环境配置...")
    
    if detect_conda():
        print("使用conda安装科学计算包...")
        
        # 安装科学计算包
        conda_packages = [
            'numpy=1.21.0',
            'scipy=1.8.0', 
            'pandas=1.4.0',
            'scikit-learn=1.0.2',
            'matplotlib=3.5.0',
            'seaborn=0.11.0',
            'tqdm=4.64.0'
        ]
        
        for package in conda_packages:
            if not run_command(f'conda install -y {package}'):
                print(f"conda安装{package}失败，尝试使用pip...")
                package_name = package.split('=')[0]
                run_command(f'pip install {package_name}')
        
        # 使用pip安装TensorFlow
        print("使用pip安装TensorFlow...")
        run_command('pip install tensorflow==2.8.0')
        run_command('pip install tensorflow-probability==0.15.0')
        
    else:
        print("conda不可用，使用pip安装所有包...")
        run_command('pip install -r requirements.txt')

def verify_installation():
    """验证安装"""
    print("验证安装...")
    
    try:
        # 测试导入
        import numpy as np
        import scipy
        import pandas as pd
        import sklearn
        import matplotlib
        import tensorflow as tf
        
        print("✓ 所有包导入成功")
        
        # 测试基本功能
        a = np.array([1, 2, 3])
        b = tf.constant([1, 2, 3])
        
        print("✓ 基本功能测试通过")
        
        # 运行完整诊断
        print("运行完整诊断...")
        run_command('python diagnose_environment.py', check=False)
        
        return True
        
    except Exception as e:
        print(f"✗ 验证失败: {e}")
        return False

def main():
    """主修复流程"""
    print("="*60)
    print("环境修复脚本")
    print("="*60)
    
    print("此脚本将:")
    print("1. 备份当前环境信息")
    print("2. 清理可能冲突的包")
    print("3. 重新安装兼容的包版本")
    print("4. 验证安装结果")
    print()
    
    choice = input("是否继续? (y/n): ")
    if choice.lower() != 'y':
        print("修复取消")
        return
    
    # 备份环境
    backup_current_environment()
    
    # 清理混用包
    clean_mixed_packages()
    
    # 重新安装
    install_safe_environment()
    
    # 验证安装
    if verify_installation():
        print("\n🎉 环境修复成功！")
        print("现在可以运行:")
        print("  python test_setup.py")
        print("  python main.py --data_name twin")
    else:
        print("\n⚠️ 环境修复可能不完整")
        print("请检查错误信息并手动解决")
        print("或尝试完全重新创建环境:")
        print("  conda env remove -n hybrid_algorithm")
        print("  conda env create -f environment_safe.yaml")

if __name__ == "__main__":
    main()
