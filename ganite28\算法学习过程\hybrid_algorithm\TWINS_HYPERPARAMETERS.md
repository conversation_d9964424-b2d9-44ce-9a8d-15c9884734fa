# Twins数据集超参数设置

## 🎯 参数修改目标

根据提供的Twins数据集超参数表格，将hybrid_algorithm阶段一模型的参数修改为最优设置。

## 📊 **超参数对照表**

| 参数类别 | 参数名 | 修改前 | 修改后 | 说明 |
|----------|--------|--------|--------|------|
| **网络架构** | `rep_dim` (dR) | 100 | **7** | I/C/A表征隐藏层宽度 |
| | `rep_layers` (hR) | 3 | **7** | I/C/A表征隐藏层层数 |
| | `gA_dim` (dY) | 50 | **64** | gA(A→Y)隐藏层宽度 |
| | `gA_layers` (hY) | 2 | **64** | gA隐藏层层数 |
| | `gI_dim` (dT) | 50 | **64** | gI(I→T)隐藏层宽度 |
| | `gI_layers` (hT) | 2 | **64** | gI隐藏层层数 |
| **损失权重** | `p_alpha` (α) | 1.0 | **1e-2** | 调整变量损失权重 |
| | `p_beta` (β) | 1.0 | **1e-3** | 工具变量损失权重 |
| | `p_mu` (μ) | 1.0 | **1e-3** | 正交正则化权重 |
| | `p_gamma` (γ) | 1.0 | **5** | 平衡损失权重 |
| | `p_lambda` (λ) | 0.001 | **5** | 权重衰减 |
| **训练设置** | `batch_norm` | True | **True** | 批归一化（保持） |
| | `rep_normalization` | True | **True** | 表征归一化（保持） |

## 🔧 **具体修改内容**

### **1. 网络架构参数**

#### **表征网络 (I/C/A)**
```python
# 修改前
self.rep_dim = 100     # dR: I/C/A 表征隐藏层宽度
self.rep_layers = 3    # hR: I/C/A 表征隐藏层层数

# 修改后 (Twins优化设置)
self.rep_dim = 7       # dR: I/C/A 表征隐藏层宽度 (Twins设置)
self.rep_layers = 7    # hR: I/C/A 表征隐藏层层数 (Twins设置)
```

#### **gA网络 (A → Y)**
```python
# 修改前
self.gA_dim = 50       # dY: gA (A → Y) 隐藏层宽度
self.gA_layers = 2     # hY: gA 隐藏层层数

# 修改后 (Twins优化设置)
self.gA_dim = 64       # dY: gA (A → Y) 隐藏层宽度 (Twins设置)
self.gA_layers = 64    # hY: gA 隐藏层层数 (Twins设置)
```

#### **gI网络 (I → T)**
```python
# 修改前
self.gI_dim = 50       # dT: gI (I → T) 隐藏层宽度
self.gI_layers = 2     # hT: gI 隐藏层层数

# 修改后 (Twins优化设置)
self.gI_dim = 64       # dT: gI (I → T) 隐藏层宽度 (Twins设置)
self.gI_layers = 64    # hT: gI 隐藏层层数 (Twins设置)
```

### **2. 损失函数权重**

```python
# 修改前
self.p_alpha = 1.0     # 调整变量损失权重
self.p_beta = 1.0      # 工具变量损失权重
self.p_mu = 1.0        # 正交正则化权重
self.p_gamma = 1.0     # 平衡损失权重
self.p_lambda = 0.001  # 权重衰减

# 修改后 (Twins优化设置)
self.p_alpha = 1e-2    # 调整变量损失权重 (α)
self.p_beta = 1e-3     # 工具变量损失权重 (β)
self.p_mu = 1e-3       # 正交正则化权重 (μ)
self.p_gamma = 5       # 平衡损失权重 (γ)
self.p_lambda = 5      # 权重衰减 (λ)
```

### **3. 训练设置确认**

```python
# 保持Twins数据集的最优设置
self.batch_norm = True         # 是否使用批归一化 (Twins: True)
self.rep_normalization = True  # 是否归一化表征 (Twins: True)
```

## 📋 **参数解释**

### **网络架构的变化**

1. **表征网络更深更窄**：
   - `rep_dim`: 100 → 7 (更窄的隐藏层)
   - `rep_layers`: 3 → 7 (更深的网络)
   - 这种设计有助于学习更精细的表征分解

2. **预测网络更宽更深**：
   - `gA_dim/gI_dim`: 50 → 64 (更宽的隐藏层)
   - `gA_layers/gI_layers`: 2 → 64 (显著更深的网络)
   - 增强了预测网络的表达能力

### **损失权重的调整**

1. **分解损失权重降低**：
   - `p_alpha`: 1.0 → 1e-2 (调整变量损失)
   - `p_beta`: 1.0 → 1e-3 (工具变量损失)
   - `p_mu`: 1.0 → 1e-3 (正交正则化)
   - 相对减少分解损失的影响

2. **平衡和正则化权重增加**：
   - `p_gamma`: 1.0 → 5 (平衡损失权重增加)
   - `p_lambda`: 0.001 → 5 (权重衰减大幅增加)
   - 更强调样本平衡和模型正则化

## 🎯 **预期效果**

### **网络架构优化**
- 更深的表征网络有助于学习更好的变量分解
- 更强的预测网络提升因果效应估计精度

### **损失权重平衡**
- 降低分解损失权重，避免过度约束
- 增强平衡和正则化，提升模型稳定性和泛化能力

### **整体性能**
- 针对Twins数据集的特点进行了专门优化
- 预期在PEHE和ATE误差指标上有显著改善

## 🚀 **使用方法**

修改完成后，可以直接运行训练：

```bash
cd hybrid_algorithm
python main.py --data_path data28/Twins38.combined.npz
```

或者使用命令行参数覆盖特定设置：

```bash
python main.py \
  --rep_dim 7 \
  --rep_layers 7 \
  --gA_dim 64 \
  --gA_layers 64 \
  --p_alpha 1e-2 \
  --p_beta 1e-3 \
  --p_mu 1e-3 \
  --p_gamma 5 \
  --p_lambda 5
```

## 📊 **验证方法**

可以通过以下方式验证参数设置：

```python
from config import get_config
config = get_config()
print(f"rep_dim: {config.rep_dim}")
print(f"rep_layers: {config.rep_layers}")
print(f"p_alpha: {config.p_alpha}")
print(f"p_beta: {config.p_beta}")
# ... 其他参数
```

这些修改确保了hybrid_algorithm使用了针对Twins数据集优化的超参数设置，应该能够获得更好的性能表现。
