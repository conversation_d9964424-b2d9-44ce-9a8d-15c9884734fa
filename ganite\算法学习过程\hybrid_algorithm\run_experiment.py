#!/usr/bin/env python3
"""
简单的实验运行脚本
"""

import os
import sys
import argparse

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='运行混合算法实验')
    
    # 基本参数
    parser.add_argument('--data_name', type=str, default='twin',
                       choices=['twin', 'ihdp', 'synthetic'],
                       help='数据集名称')
    parser.add_argument('--quick_test', action='store_true',
                       help='快速测试模式（较少的训练轮数）')
    parser.add_argument('--gpu', type=int, default=0,
                       help='GPU设备号')
    
    args = parser.parse_args()
    
    # 设置GPU
    if args.gpu >= 0:
        os.environ['CUDA_VISIBLE_DEVICES'] = str(args.gpu)
    
    # 构建命令
    cmd_args = [
        '--data_name', args.data_name,
        '--seed', '3407',
        '--device', 'cuda' if args.gpu >= 0 else 'cpu'
    ]
    
    if args.quick_test:
        cmd_args.extend([
            '--stage1_epochs', '500',
            '--stage2_epochs', '500',
            '--eval_freq', '20',
            '--save_freq', '50'
        ])
    else:
        cmd_args.extend([
            '--stage1_epochs', '1000',
            '--stage2_epochs', '500',
            '--eval_freq', '100',
            '--save_freq', '200'
        ])
    
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    main_py_path = os.path.join(script_dir, 'main.py')

    # 运行主脚本
    cmd = ['python', main_py_path] + cmd_args
    print(f"运行命令: {' '.join(cmd)}")

    # 切换到脚本目录执行（确保相对路径正确）
    original_cwd = os.getcwd()
    os.chdir(script_dir)

    try:
        os.system(' '.join(cmd))
    finally:
        # 恢复原始工作目录
        os.chdir(original_cwd)

if __name__ == '__main__':
    main()
