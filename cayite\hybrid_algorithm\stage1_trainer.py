"""
阶段一训练器：DeR-CFR增强的表征分解 + GAN生成
"""

import tensorflow as tf
import numpy as np
import os
from tqdm import tqdm
import logging

from models.stage1_model import Stage1Model
from data_loader import batch_generator
from evaluation import evaluate_model, print_evaluation_results

class Stage1Trainer:
    """阶段一训练器"""
    
    def __init__(self, config):
        self.config = config
        
        # 设置随机种子
        tf.random.set_seed(config.seed)
        np.random.seed(config.seed)
        
        # 创建模型
        self.model = Stage1Model(config)
        
        # 创建优化器（阶段一GAN使用分离的学习率）
        self.optimizer_main = tf.keras.optimizers.Adam(
            learning_rate=config.learning_rate,  # 表征网络+生成器学习率
            beta_1=0.5,
            beta_2=0.9
        )

        self.optimizer_disc = tf.keras.optimizers.<PERSON>(
            learning_rate=config.stage1_disc_lr,  # 判别器专用学习率
            beta_1=0.5,
            beta_2=0.9
        )

        self.optimizer_weights = tf.keras.optimizers.Adam(
            learning_rate=config.w_lr,  # 样本权重专用学习率
            beta_1=0.5,
            beta_2=0.9
        )
        
        # 设置日志
        self.setup_logging()
        
        # 训练历史
        self.train_history = {
            'total_loss': [],
            'decomposition_loss': [],
            'adversarial_loss': [],
            'factual_loss': [],
            'pehe': [],
            'ate_error': []
        }
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=getattr(logging, self.config.log_level),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(self.config.output_dir, 'logs', 'stage1_training.log')),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    @tf.function
    def train_discriminator_step(self, x, t, y):
        """【全新】训练判别器的一步：判断档案真伪"""
        with tf.GradientTape() as tape:
            # 生成表征和伪造结果
            outputs = self.model([x, t, y], training=True)
            rep_C = tf.stop_gradient(outputs['rep_C'])
            rep_A = tf.stop_gradient(outputs['rep_A'])
            y_logits = tf.stop_gradient(outputs['y_logits'])

            # 构造"伪造档案"的结论部分
            y_0_logit, y_1_logit = y_logits[:, 0:1], y_logits[:, 1:2]
            y_generated_factual = t * y_1_logit + (1 - t) * y_0_logit

            # 构造判别器的输入
            # 1. 真实档案输入
            real_input = tf.concat([rep_C, rep_A, y], axis=1)
            # 2. 伪造档案输入
            fake_input = tf.concat([rep_C, rep_A, y_generated_factual], axis=1)

            # 判别器打分
            real_logit = self.model.discriminator(real_input, training=True)
            fake_logit = self.model.discriminator(fake_input, training=True)

            # 计算判别器损失 (real -> 1, fake -> 0)
            real_loss = tf.nn.sigmoid_cross_entropy_with_logits(labels=tf.ones_like(real_logit), logits=real_logit)
            fake_loss = tf.nn.sigmoid_cross_entropy_with_logits(labels=tf.zeros_like(fake_logit), logits=fake_logit)
            d_loss = tf.reduce_mean(real_loss + fake_loss)

            # L2正则化
            d_l2_reg = self.config.p_lambda * tf.add_n(
              [tf.nn.l2_loss(v) for v in self.model.discriminator.trainable_variables])
            d_total_loss = d_loss + d_l2_reg

        # 计算梯度并更新（只更新判别器参数）
        d_grads = tape.gradient(d_total_loss, self.model.discriminator.trainable_variables)
        self.optimizer_disc.apply_gradients(
            zip(d_grads, self.model.discriminator.trainable_variables))
        return d_total_loss
    
    @tf.function
    def train_main_step(self, x, t, y, batch_indices):
        """训练主网络（表征网络+生成器）的一步"""
        with tf.GradientTape() as tape:
            # 前向传播
            outputs = self.model([x, t, y], training=True)
            rep_I = outputs['rep_I']
            rep_C = outputs['rep_C']
            rep_A = outputs['rep_A']
            y_logits = outputs['y_logits']
            
            # 获取当前批次的样本权重（完全按照DeR-CFR原版）
            current_weights = self.model.get_sample_weights(batch_indices)

            # 计算各项损失（所有IPM/工具变量损失都传入weights）

            # 1. 调整变量损失 L_A（传入权重）
            L_A = self.model.compute_adjustment_loss(rep_A, t, y, weights=current_weights)


            # 2. 工具变量损失 L_I（传入权重）
            L_I = self.model.compute_instrumental_loss(rep_I, t, y, weights=current_weights)

            # 3. 正交损失 L_O
            L_O = self.model.compute_orthogonal_loss()

            # 4. 事实监督损失（关键：使用样本权重加权，实现相互反馈机制）
            factual_loss = self.model.compute_factual_loss(y_logits, y, t, weights=current_weights)

            # 5. 【修改】新的对抗损失：欺骗档案鉴别器
            y_0_logit, y_1_logit = y_logits[:, 0:1], y_logits[:, 1:2]
            y_generated_factual = t * y_1_logit + (1 - t) * y_0_logit
            fake_input = tf.concat([rep_C, rep_A, y_generated_factual], axis=1)

            # 判别器对伪造档案打分
            fake_logit = self.model.discriminator(fake_input, training=True)

            # 生成器的目标是让判别器相信它是真的 (fake -> 1)
            adversarial_loss = tf.reduce_mean(
                tf.nn.sigmoid_cross_entropy_with_logits(labels=tf.ones_like(fake_logit), logits=fake_logit)
            )
            
            # 总损失
            decomposition_loss = (
                self.config.p_alpha * L_A +
                self.config.p_beta * L_I +
                self.config.p_mu * L_O
            )
            
            # ① 选出需要正则的变量：排除样本权重 & BN 统计量
            l2_vars = [v for v in self.model.trainable_variables
                    if ('sample_weights' not in v.name and
                        'moving_mean'   not in v.name and
                        'moving_variance' not in v.name)]

            # ②  λ·‖θ‖²  （tf.nn.l2_loss = ½·∑w²）
            l2_reg = tf.constant(0.0)
            if l2_vars:        # 防空列表错误
                l2_reg = self.config.p_lambda * tf.add_n(
                            [tf.nn.l2_loss(v) for v in l2_vars])

            total_loss = (
                decomposition_loss +
                factual_loss +
                self.config.p_adv * adversarial_loss +  # 可配置的对抗损失权重
                l2_reg
            )

        
        # 计算梯度并更新（表征网络+生成器层）
        # 修正：排除判别器和样本权重，避免冲突性更新
        trainable_vars = [v for v in self.model.trainable_variables
                         if not (v.name.startswith('stage1_discriminator/') or
                                v.name.endswith('sample_weights:0'))]
        
        grads = tape.gradient(total_loss, trainable_vars)
        self.optimizer_main.apply_gradients(zip(grads, trainable_vars))
        
        return {
            'total_loss': total_loss,
            'decomposition_loss': decomposition_loss,
            'adversarial_loss': adversarial_loss,
            'factual_loss': factual_loss,
            'weight_reg': l2_reg,
            'L_A': L_A,
            'L_I': L_I,
            'L_O': L_O
        }
    
    @tf.function
    def train_weights_step(self, x, t, y, batch_indices):
        """训练样本权重的一步（如果启用）"""
        if not self.config.reweight_sample:
            return tf.constant(0.0)

        with tf.GradientTape() as tape:
            # 前向传播获取混淆表征
            outputs = self.model([x, t, y], training=True)
            rep_C = outputs['rep_C']

            # 获取当前样本权重（按照DeR-CFR原版）
            current_weights = self.model.get_sample_weights(batch_indices)

            # 计算混淆变量平衡损失（使用当前权重）
            balance_loss = self.model.compute_ipm_loss(rep_C, t, weights=current_weights)

            # 权重正则化（完全按照DeR-CFR原版：按治疗臂分离）
            # 分离治疗组和对照组的权重
            i0 = tf.where(t < 0.5)[:, 0]  # 对照组索引
            i1 = tf.where(t >= 0.5)[:, 0]  # 治疗组索引

            weights_0 = tf.gather(current_weights, i0)  # 对照组权重
            weights_1 = tf.gather(current_weights, i1)  # 治疗组权重

            # 按照DeR-CFR原版：(Σω₀-1)²+(Σω₁-1)²
            weight_reg_0 = tf.square(tf.reduce_sum(weights_0) / (tf.cast(tf.shape(weights_0)[0], tf.float32) + 1e-8) - 1.0)
            weight_reg_1 = tf.square(tf.reduce_sum(weights_1) / (tf.cast(tf.shape(weights_1)[0], tf.float32) + 1e-8) - 1.0)
            weight_reg = weight_reg_0 + weight_reg_1

            total_loss = balance_loss + self.config.p_gamma * weight_reg


        # 更新样本权重
        grads = tape.gradient(total_loss, [self.model.sample_weights])
        if grads[0] is not None:
            self.optimizer_weights.apply_gradients(
                zip(grads, [self.model.sample_weights])
            )

        return total_loss
    
    def train_epoch(self, train_x, train_t, train_y):
        """训练一个epoch"""
        n_samples = len(train_x)
        n_batches = n_samples // self.config.batch_size

        epoch_losses = {
            'total_loss': 0,
            'decomposition_loss': 0,
            'adversarial_loss': 0,
            'factual_loss': 0,
            'd_loss': 0,
            'balance_loss': 0,
            'weight_reg': 0
        }

        # 创建样本索引
        indices = tf.range(n_samples)
        indices = tf.random.shuffle(indices)

        for i in range(n_batches):
            # 获取批次索引
            start_idx = i * self.config.batch_size
            end_idx = start_idx + self.config.batch_size
            batch_indices = indices[start_idx:end_idx]

            # 获取批次数据
            batch_x = tf.gather(train_x, batch_indices)
            batch_t = tf.gather(train_t, batch_indices)
            batch_y = tf.gather(train_y, batch_indices)

            # 转换为tensor
            batch_x = tf.cast(batch_x, tf.float32)
            batch_t = tf.cast(batch_t, tf.float32)
            batch_y = tf.cast(batch_y, tf.float32)

            # 训练判别器
            d_loss = self.train_discriminator_step(batch_x, batch_t, batch_y)

            # 训练主网络（传入批次索引）
            main_losses = self.train_main_step(batch_x, batch_t, batch_y, batch_indices)

            # 训练样本权重（如果启用，传入批次索引）
            balance_loss = self.train_weights_step(batch_x, batch_t, batch_y, batch_indices)

            # 累积损失
            epoch_losses['total_loss'] += main_losses['total_loss']
            epoch_losses['decomposition_loss'] += main_losses['decomposition_loss']
            epoch_losses['adversarial_loss'] += main_losses['adversarial_loss']
            epoch_losses['factual_loss'] += main_losses['factual_loss']
            epoch_losses['d_loss'] += d_loss
            epoch_losses['balance_loss'] += balance_loss
            epoch_losses['weight_reg'] += main_losses.get('weight_reg', 0.0)
        
        # 平均损失
        for key in epoch_losses:
            epoch_losses[key] /= n_batches
        
        return epoch_losses
    
    def train(self, train_data, test_data):
        """
        训练阶段一模型
        
        Args:
            train_data: (train_x, train_t, train_y, train_potential_y)
            test_data: (test_x, test_t, test_y, test_potential_y)
        """
        train_x, train_t, train_y, train_potential_y = train_data
        test_x, test_t, test_y, test_potential_y = test_data

        # 初始化样本权重（完全按照DeR-CFR原版）
        n_samples = len(train_x)
        self.model.build_sample_weights(n_samples)

        self.logger.info("Starting Stage 1 training...")
        self.logger.info(f"Training samples: {len(train_x)}")
        self.logger.info(f"Test samples: {len(test_x)}")
        self.logger.info(f"Sample weights initialized: shape={self.model.sample_weights.shape}")
        
        best_pehe = float('inf')
        patience_counter = 0
        
        for epoch in range(self.config.stage1_epochs):
            # 训练一个epoch
            epoch_losses = self.train_epoch(train_x, train_t, train_y)
            
            # 记录损失
            for key, value in epoch_losses.items():
                if key in self.train_history:
                    self.train_history[key].append(float(value))
            
            # 定期评估
            if epoch % self.config.eval_freq == 0:
                # 评估模型
                eval_results = evaluate_model(
                    self.model, test_x, test_t, test_y, test_potential_y, stage='stage1'
                )
                
                # 记录评估结果
                self.train_history['pehe'].append(eval_results['pehe'])
                self.train_history['ate_error'].append(eval_results['ate_error'])
                
                # 打印结果
                self.logger.info(f"Epoch {epoch}:")
                self.logger.info(f"  Total Loss: {epoch_losses['total_loss']:.6f}")
                self.logger.info(f"  PEHE: {eval_results['pehe']:.6f}")
                self.logger.info(f"  ATE Error: {eval_results['ate_error']:.6f}")

                # 详细损失分解
                self.logger.info(f"    - Main Losses (G): Factual={epoch_losses['factual_loss']:.4f}, Adversarial={epoch_losses['adversarial_loss']:.4f}, Decomp={epoch_losses['decomposition_loss']:.4f}")
                self.logger.info(f"    - Discriminator Loss (D): {epoch_losses['d_loss']:.4f}")
                self.logger.info(f"    - Balance Loss (Weights): {epoch_losses['balance_loss']:.4f}")
                self.logger.info(f"    - Weight Regularization: {epoch_losses['weight_reg']:.4f}")
                
                # 早停检查
                if eval_results['pehe'] < best_pehe:
                    best_pehe = eval_results['pehe']
                    patience_counter = 0
                    
                    # 保存最佳模型
                    self.save_model('best_stage1_model')
                else:
                    patience_counter += 1
                
                if patience_counter >= self.config.early_stop_patience:
                    self.logger.info(f"Early stopping at epoch {epoch}")
                    break
            
            # 定期保存
            if epoch % self.config.save_freq == 0:
                self.save_model(f'stage1_model_epoch_{epoch}')
        
        self.logger.info("Stage 1 training completed!")
        return self.train_history
    
    def save_model(self, model_name):
        """保存模型"""
        save_path = os.path.join(self.config.output_dir, 'models', model_name)
        self.model.save_weights(save_path)
        self.logger.info(f"Model saved to {save_path}")
    
    def load_model(self, model_path):
        """加载模型"""
        self.model.load_weights(model_path)
        self.logger.info(f"Model loaded from {model_path}")
    
    def generate_stage1_outputs(self, x, t, y):
        """生成阶段一的输出（用于阶段二）"""
        outputs = self.model([x, t, y], training=False)
        return outputs['y_logits']
