#!/usr/bin/env python3
"""
环境诊断脚本：检测conda/pip混用问题和二进制兼容性
"""

import sys
import os
import subprocess
import importlib
import pkg_resources

def get_package_info(package_name):
    """获取包的详细信息"""
    try:
        # 获取包的安装信息
        dist = pkg_resources.get_distribution(package_name)
        
        # 尝试导入包
        module = importlib.import_module(package_name)
        
        # 获取包的安装路径
        install_path = dist.location
        
        # 检查是否通过conda安装
        is_conda = 'conda' in install_path or 'anaconda' in install_path
        
        info = {
            'name': package_name,
            'version': dist.version,
            'location': install_path,
            'installer': 'conda' if is_conda else 'pip',
            'module_file': getattr(module, '__file__', 'N/A')
        }
        
        return info
        
    except Exception as e:
        return {
            'name': package_name,
            'error': str(e),
            'installed': False
        }

def check_binary_compatibility():
    """检查二进制兼容性"""
    print("检查二进制兼容性...")
    
    try:
        import numpy as np
        import scipy
        import sklearn
        
        # 测试基本运算
        a = np.array([1, 2, 3])
        b = np.array([4, 5, 6])
        c = np.dot(a, b)
        print(f"✓ NumPy基本运算测试通过: {c}")
        
        # 测试SciPy
        from scipy import linalg
        matrix = np.array([[1, 2], [3, 4]])
        det = linalg.det(matrix)
        print(f"✓ SciPy线性代数测试通过: det = {det}")
        
        # 测试scikit-learn
        from sklearn.datasets import make_classification
        X, y = make_classification(n_samples=100, n_features=4, random_state=42)
        print(f"✓ Scikit-learn数据生成测试通过: X.shape = {X.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 二进制兼容性测试失败: {e}")
        return False

def check_tensorflow_compatibility():
    """检查TensorFlow兼容性"""
    print("\n检查TensorFlow兼容性...")
    
    try:
        import tensorflow as tf
        print(f"✓ TensorFlow版本: {tf.__version__}")
        
        # 检查GPU支持
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            print(f"✓ GPU支持: {len(gpus)} 个设备")
        else:
            print("⚠ 无GPU支持")
        
        # 测试基本运算
        a = tf.constant([1, 2, 3])
        b = tf.constant([4, 5, 6])
        c = tf.add(a, b)
        print(f"✓ TensorFlow基本运算测试通过: {c.numpy()}")
        
        # 测试与NumPy的兼容性
        import numpy as np
        np_array = np.array([1, 2, 3])
        tf_tensor = tf.constant(np_array)
        back_to_np = tf_tensor.numpy()
        print(f"✓ TensorFlow-NumPy兼容性测试通过")
        
        return True
        
    except Exception as e:
        print(f"✗ TensorFlow兼容性测试失败: {e}")
        return False

def analyze_package_sources():
    """分析包的安装来源"""
    print("\n分析包的安装来源...")
    
    critical_packages = [
        'numpy', 'scipy', 'pandas', 'sklearn', 
        'matplotlib', 'tensorflow'
    ]
    
    conda_packages = []
    pip_packages = []
    mixed_detected = False
    
    for package in critical_packages:
        info = get_package_info(package)
        
        if 'error' in info:
            print(f"⚠ {package}: 未安装或导入失败")
            continue
        
        installer = info['installer']
        print(f"{package}: {info['version']} ({installer})")
        print(f"  位置: {info['location']}")
        
        if installer == 'conda':
            conda_packages.append(package)
        else:
            pip_packages.append(package)
    
    # 检查是否存在混用
    if conda_packages and pip_packages:
        mixed_detected = True
        print(f"\n⚠️  检测到conda/pip混用:")
        print(f"  Conda安装: {conda_packages}")
        print(f"  Pip安装: {pip_packages}")
        print(f"  这可能导致二进制兼容性问题！")
    else:
        print(f"\n✓ 包管理器使用一致")
        if conda_packages:
            print(f"  所有包都通过conda安装")
        else:
            print(f"  所有包都通过pip安装")
    
    return mixed_detected

def suggest_fixes():
    """建议修复方案"""
    print("\n" + "="*50)
    print("修复建议")
    print("="*50)
    
    print("如果检测到兼容性问题，建议采取以下措施:")
    print()
    print("1. 重新创建纯conda环境:")
    print("   conda env remove -n hybrid_algorithm")
    print("   conda env create -f environment_safe.yaml")
    print()
    print("2. 或者重新创建纯pip环境:")
    print("   pip uninstall numpy scipy pandas scikit-learn matplotlib tensorflow")
    print("   pip install -r requirements.txt")
    print()
    print("3. 检查CUDA版本兼容性 (如果使用GPU):")
    print("   nvidia-smi")
    print("   python -c \"import tensorflow as tf; print(tf.config.list_physical_devices('GPU'))\"")
    print()
    print("4. 如果问题持续存在，尝试:")
    print("   - 更新conda: conda update conda")
    print("   - 清理缓存: conda clean --all")
    print("   - 重启Python解释器")

def main():
    """主诊断函数"""
    print("="*60)
    print("环境兼容性诊断")
    print("="*60)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 检查包安装来源
    mixed_detected = analyze_package_sources()
    
    # 检查二进制兼容性
    binary_ok = check_binary_compatibility()
    
    # 检查TensorFlow兼容性
    tf_ok = check_tensorflow_compatibility()
    
    # 总结
    print("\n" + "="*60)
    print("诊断总结")
    print("="*60)
    
    if mixed_detected:
        print("⚠️  检测到conda/pip混用，可能存在兼容性风险")
    else:
        print("✓ 包管理器使用一致")
    
    if binary_ok:
        print("✓ 科学计算包二进制兼容性正常")
    else:
        print("✗ 科学计算包存在兼容性问题")
    
    if tf_ok:
        print("✓ TensorFlow兼容性正常")
    else:
        print("✗ TensorFlow存在兼容性问题")
    
    # 给出建议
    if mixed_detected or not binary_ok or not tf_ok:
        suggest_fixes()
    else:
        print("\n🎉 环境配置良好，可以正常使用！")

if __name__ == "__main__":
    main()
