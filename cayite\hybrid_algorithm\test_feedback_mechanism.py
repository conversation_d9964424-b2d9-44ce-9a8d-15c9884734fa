#!/usr/bin/env python3
"""
测试样本权重相互反馈机制
验证分解部分和GAN部分是否能够相互调节
"""

import tensorflow as tf
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

from config import Config
from models.stage1_model import Stage1Model

def test_feedback_mechanism():
    """测试相互反馈机制"""
    print("="*60)
    print("测试样本权重相互反馈机制")
    print("="*60)
    
    # 创建配置
    config = Config()
    config.x_dim = 30
    config.rep_dim = 20
    config.h_dim = 64
    config.reweight_sample = True
    
    # 创建模型
    model = Stage1Model(config)
    
    # 创建模拟数据
    batch_size = 32
    x = tf.random.normal([batch_size, config.x_dim])
    t = tf.random.uniform([batch_size, 1], maxval=2, dtype=tf.int32)
    t = tf.cast(t, tf.float32)
    y = tf.random.uniform([batch_size, 1], maxval=2, dtype=tf.int32)
    y = tf.cast(y, tf.float32)
    
    print(f"数据形状: X={x.shape}, T={t.shape}, Y={y.shape}")
    
    # 测试前向传播
    print("\n1. 测试前向传播...")
    outputs = model([x, t, y], training=True)
    
    print(f"✓ 表征I形状: {outputs['rep_I'].shape}")
    print(f"✓ 表征C形状: {outputs['rep_C'].shape}")
    print(f"✓ 表征A形状: {outputs['rep_A'].shape}")
    print(f"✓ 潜在结果形状: {outputs['y_logits'].shape}")
    print(f"✓ 判别器输出形状: {outputs['d_logit'].shape}")
    
    # 测试样本权重
    print("\n2. 测试样本权重机制...")
    initial_weights = model.get_sample_weights(batch_size)
    print(f"✓ 初始样本权重: {initial_weights[:5].numpy()}")
    
    # 测试加权的事实损失
    print("\n3. 测试加权事实损失...")
    factual_loss_unweighted = model.compute_factual_loss(outputs['y_logits'], y, t)
    factual_loss_weighted = model.compute_factual_loss(outputs['y_logits'], y, t, weights=initial_weights)
    
    print(f"✓ 无权重事实损失: {factual_loss_unweighted:.6f}")
    print(f"✓ 加权事实损失: {factual_loss_weighted:.6f}")
    
    # 测试加权的IPM损失
    print("\n4. 测试加权IPM损失...")
    ipm_loss_unweighted = model.compute_ipm_loss(outputs['rep_C'], t)
    ipm_loss_weighted = model.compute_ipm_loss(outputs['rep_C'], t, weights=initial_weights)
    
    print(f"✓ 无权重IPM损失: {imp_loss_unweighted:.6f}")
    print(f"✓ 加权IPM损失: {ipm_loss_weighted:.6f}")
    
    # 测试梯度流
    print("\n5. 测试梯度流...")
    with tf.GradientTape() as tape:
        outputs = model([x, t, y], training=True)
        current_weights = model.get_sample_weights(batch_size)
        
        # 计算总损失（模拟训练过程）
        factual_loss = model.compute_factual_loss(outputs['y_logits'], y, t, weights=current_weights)
        ipm_loss = model.compute_ipm_loss(outputs['rep_C'], t, weights=current_weights)
        
        total_loss = factual_loss + ipm_loss
    
    # 检查梯度
    grads_weights = tape.gradient(total_loss, [model.sample_weights])
    grads_rep_c = tape.gradient(total_loss, model.rep_C.trainable_variables)
    grads_generator = tape.gradient(total_loss, model.generator.trainable_variables)
    
    print(f"✓ 样本权重梯度: {grads_weights[0] is not None}")
    print(f"✓ 表征C梯度: {len([g for g in grads_rep_c if g is not None])} / {len(grads_rep_c)}")
    print(f"✓ 生成器梯度: {len([g for g in grads_generator if g is not None])} / {len(grads_generator)}")
    
    # 测试相互反馈
    print("\n6. 测试相互反馈机制...")
    
    # 模拟权重更新
    optimizer_weights = tf.keras.optimizers.Adam(learning_rate=0.01)
    optimizer_main = tf.keras.optimizers.Adam(learning_rate=0.001)
    
    print("初始状态:")
    print(f"  样本权重: {model.sample_weights.numpy()}")
    
    # 步骤1: 更新样本权重（基于rep_C的平衡损失）
    with tf.GradientTape() as tape:
        outputs = model([x, t, y], training=True)
        current_weights = model.get_sample_weights(batch_size)
        balance_loss = model.compute_ipm_loss(outputs['rep_C'], t, weights=current_weights)
        weight_reg = tf.square(tf.reduce_mean(current_weights) - 1.0)
        weight_loss = balance_loss + 0.1 * weight_reg
    
    grads = tape.gradient(weight_loss, [model.sample_weights])
    if grads[0] is not None:
        optimizer_weights.apply_gradients(zip(grads, [model.sample_weights]))
        print(f"✓ 权重更新成功")
        print(f"  更新后权重: {model.sample_weights.numpy()}")
    
    # 步骤2: 使用更新后的权重训练主网络
    with tf.GradientTape() as tape:
        outputs = model([x, t, y], training=True)
        updated_weights = model.get_sample_weights(batch_size)
        
        # 使用更新后的权重计算损失
        factual_loss = model.compute_factual_loss(outputs['y_logits'], y, t, weights=updated_weights)
        ipm_loss = model.compute_ipm_loss(outputs['rep_A'], t)
        
        main_loss = factual_loss + imp_loss
    
    # 更新主网络
    trainable_vars = (
        model.rep_I.trainable_variables +
        model.rep_C.trainable_variables +
        model.rep_A.trainable_variables +
        model.generator.trainable_variables
    )
    
    grads = tape.gradient(main_loss, trainable_vars)
    valid_grads = [g for g in grads if g is not None]
    
    if len(valid_grads) > 0:
        print(f"✓ 主网络梯度计算成功: {len(valid_grads)} / {len(grads)}")
        print("✓ 相互反馈机制正常工作！")
    else:
        print("✗ 主网络梯度计算失败")
    
    print("\n" + "="*60)
    print("相互反馈机制测试总结")
    print("="*60)
    print("✓ 样本权重变量已创建")
    print("✓ 加权损失函数正常工作")
    print("✓ 梯度流通畅")
    print("✓ 分解部分 → 样本权重 → GAN部分 的反馈链路完整")
    print("\n🎉 相互反馈机制实现成功！")
    print("   分解部分和GAN部分能够通过样本权重相互调节")

if __name__ == "__main__":
    test_feedback_mechanism()
