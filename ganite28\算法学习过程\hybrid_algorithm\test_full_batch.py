"""
测试全批次训练的批次生成器
"""

import numpy as np
from npzdata import load_twins_npz_data, create_derc_batch_generator, get_npz_data_info

def test_full_batch_training():
    """测试全批次训练"""
    print("="*60)
    print("测试全批次训练的批次生成器")
    print("="*60)
    
    try:
        # 加载数据
        print("1. 加载NPZ数据...")
        data_loader = load_twins_npz_data(
            data_path='../data28/Twins38.combined.npz',
            train_valid_test=[63, 27, 10],
            seed=123,
            ind=0
        )
        
        # 获取数据信息
        data_info = get_npz_data_info(data_loader)
        print(f"\n数据信息:")
        print(f"  训练样本数: {data_info['train_samples']}")
        
        # 测试全批次训练（批次大小大于样本数）
        print("\n2. 测试全批次训练...")
        large_batch_size = 5271  # 大于训练样本数3320
        batch_generator = create_derc_batch_generator(data_loader, large_batch_size)
        
        print(f"配置的批次大小: {large_batch_size}")
        print(f"实际批次大小: {batch_generator.batch_size}")
        print(f"每个epoch的批次数: {batch_generator.batch_num}")
        
        # 验证是否为全批次训练
        assert batch_generator.batch_size == data_info['train_samples'], "批次大小应该等于训练样本数"
        assert batch_generator.batch_num == 1, "全批次训练每个epoch应该只有1个批次"
        
        # 生成几个批次测试
        print("\n3. 生成测试批次...")
        for i in range(3):
            x, t, y, I, ycf, mu0, mu1 = batch_generator.batch.__next__()
            
            print(f"\n批次 {i+1}:")
            print(f"  x shape: {x.shape}")
            print(f"  样本数: {x.shape[0]}")
            print(f"  是否包含所有训练样本: {x.shape[0] == data_info['train_samples']}")
            
            # 验证索引范围
            print(f"  索引范围: [{np.min(I)}, {np.max(I)}]")
            print(f"  唯一索引数: {len(np.unique(I))}")
            
            # 验证数据完整性
            assert x.shape[0] == data_info['train_samples'], f"批次大小不正确: {x.shape[0]} != {data_info['train_samples']}"
            assert len(np.unique(I)) == data_info['train_samples'], "索引应该包含所有训练样本"
            
        print("\n4. 验证数据打乱...")
        # 获取两个连续批次的索引
        x1, t1, y1, I1, ycf1, mu01, mu11 = batch_generator.batch.__next__()
        x2, t2, y2, I2, ycf2, mu02, mu12 = batch_generator.batch.__next__()
        
        # 检查数据是否被打乱（索引顺序不同）
        indices_different = not np.array_equal(I1, I2)
        print(f"  连续批次间数据打乱: {'✓' if indices_different else '✗'}")
        
        # 验证数据内容一致性（打乱后应该包含相同的样本）
        sorted_I1 = np.sort(I1.flatten())
        sorted_I2 = np.sort(I2.flatten())
        indices_same_content = np.array_equal(sorted_I1, sorted_I2)
        print(f"  数据内容一致性: {'✓' if indices_same_content else '✗'}")
        
        print("\n5. 性能测试...")
        import time
        start_time = time.time()
        
        # 模拟训练10个epoch
        for epoch in range(10):
            x, t, y, I, ycf, mu0, mu1 = batch_generator.batch.__next__()
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"  10个epoch耗时: {elapsed_time:.4f} 秒")
        print(f"  平均每epoch: {elapsed_time/10:.4f} 秒")
        
        print("\n" + "="*60)
        print("✅ 全批次训练测试成功!")
        print("="*60)
        
        # 总结
        print(f"\n📊 全批次训练配置总结:")
        print(f"  训练样本数: {data_info['train_samples']}")
        print(f"  配置批次大小: {large_batch_size}")
        print(f"  实际批次大小: {batch_generator.batch_size}")
        print(f"  每个epoch批次数: {batch_generator.batch_num}")
        print(f"  训练方式: 全批次梯度下降")
        print(f"  数据打乱: 每个epoch自动打乱")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_normal_batch_training():
    """对比测试正常批次训练"""
    print("\n" + "="*60)
    print("对比测试正常批次训练")
    print("="*60)
    
    try:
        # 加载数据
        data_loader = load_twins_npz_data(
            data_path='../data28/Twins38.combined.npz',
            train_valid_test=[63, 27, 10],
            seed=123,
            ind=0
        )
        
        data_info = get_npz_data_info(data_loader)
        
        # 测试正常批次训练
        normal_batch_size = 256
        batch_generator = create_derc_batch_generator(data_loader, normal_batch_size)
        
        print(f"正常批次训练:")
        print(f"  配置批次大小: {normal_batch_size}")
        print(f"  实际批次大小: {batch_generator.batch_size}")
        print(f"  每个epoch批次数: {batch_generator.batch_num}")
        print(f"  训练方式: 小批次梯度下降")
        
        # 验证批次数计算
        expected_batches = np.ceil(data_info['train_samples'] / normal_batch_size)
        assert batch_generator.batch_num == expected_batches, f"批次数计算错误: {batch_generator.batch_num} != {expected_batches}"
        
        print("✅ 正常批次训练验证成功!")
        
    except Exception as e:
        print(f"❌ 正常批次训练测试失败: {e}")

if __name__ == "__main__":
    # 测试全批次训练
    success = test_full_batch_training()
    
    if success:
        # 对比测试正常批次训练
        test_normal_batch_training()
    
    print("\n测试完成!")
