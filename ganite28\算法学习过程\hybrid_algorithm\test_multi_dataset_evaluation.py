"""
测试多数据集评估功能
"""

import numpy as np
import tensorflow as tf
from npzdata import load_twins_npz_data, get_npz_data_info
from evaluation import (evaluate_model, evaluate_model_on_multiple_datasets,
                       print_multi_dataset_results, save_multi_dataset_results)
from models.stage1_model import Stage1Model
from config import get_config

def create_dummy_model(config):
    """创建一个简单的测试模型"""
    
    class DummyModel:
        def __init__(self, config):
            self.config = config
            
        def __call__(self, inputs, training=False):
            x, t, y = inputs
            batch_size = tf.shape(x)[0]
            
            # 返回随机的潜在结果预测
            y_logits = tf.random.normal([batch_size, 2])
            
            return {
                'y_logits': y_logits,
                'rep_I': tf.random.normal([batch_size, config.rep_dim]),
                'rep_C': tf.random.normal([batch_size, config.rep_dim]),
                'rep_A': tf.random.normal([batch_size, config.rep_dim]),
                'd_logit': tf.random.normal([batch_size, 1])
            }
    
    return DummyModel(config)

def test_multi_dataset_evaluation():
    """测试多数据集评估功能"""
    print("="*60)
    print("测试多数据集评估功能")
    print("="*60)
    
    try:
        # 获取配置
        config = get_config()
        config.x_dim = 38  # 设置特征维度
        
        # 加载数据
        print("1. 加载NPZ数据...")
        data_loader = load_twins_npz_data(
            data_path='../data28/Twins38.combined.npz',
            train_valid_test=[63, 27, 10],
            seed=123,
            ind=0
        )
        
        # 获取数据信息
        data_info = get_npz_data_info(data_loader)
        print(f"\n数据信息:")
        for key, value in data_info.items():
            print(f"  {key}: {value}")
        
        # 创建测试模型
        print("\n2. 创建测试模型...")
        model = create_dummy_model(config)
        
        # 进行多数据集评估
        print("\n3. 进行多数据集评估...")
        multi_results = evaluate_model_on_multiple_datasets(
            model, data_loader, stage='stage1'
        )
        
        # 打印结果
        print("\n4. 打印评估结果...")
        print_multi_dataset_results(multi_results, stage='stage1')
        
        # 验证结果结构
        print("\n5. 验证结果结构...")
        expected_datasets = ['train', 'valid', 'test']
        expected_metrics = ['pehe', 'ate_error', 'mse_y0', 'mse_y1', 'factual_mse', 'true_ate', 'pred_ate']
        
        for dataset in expected_datasets:
            assert dataset in multi_results, f"缺少数据集: {dataset}"
            print(f"  ✓ {dataset} 数据集结果存在")
            
            for metric in expected_metrics:
                assert metric in multi_results[dataset], f"缺少指标: {metric} in {dataset}"
                print(f"    ✓ {metric}: {multi_results[dataset][metric]:.6f}")
        
        # 测试保存功能
        print("\n6. 测试保存功能...")
        import tempfile
        import os
        
        with tempfile.TemporaryDirectory() as temp_dir:
            save_path = save_multi_dataset_results(multi_results, temp_dir, stage='test_stage1')
            
            # 验证文件是否创建
            assert os.path.exists(save_path), "主结果文件未创建"
            print(f"  ✓ 主结果文件已创建: {save_path}")
            
            # 验证各数据集文件是否创建
            for dataset in expected_datasets:
                dataset_file = os.path.join(temp_dir, f'test_stage1_{dataset}_results.json')
                assert os.path.exists(dataset_file), f"{dataset}结果文件未创建"
                print(f"  ✓ {dataset}结果文件已创建: {dataset_file}")
        
        # 测试数据一致性
        print("\n7. 测试数据一致性...")
        
        # 验证样本数量
        train_samples = multi_results['train']['true_ate']  # 这里只是检查是否有值
        valid_samples = multi_results['valid']['true_ate']
        test_samples = multi_results['test']['true_ate']
        
        print(f"  训练集真实ATE: {train_samples:.6f}")
        print(f"  验证集真实ATE: {valid_samples:.6f}")
        print(f"  测试集真实ATE: {test_samples:.6f}")
        
        # 验证指标范围合理性
        for dataset in expected_datasets:
            pehe = multi_results[dataset]['pehe']
            ate_error = multi_results[dataset]['ate_error']
            
            assert pehe >= 0, f"{dataset} PEHE应该非负"
            assert ate_error >= 0, f"{dataset} ATE Error应该非负"
            
            print(f"  ✓ {dataset} 指标范围合理: PEHE={pehe:.6f}, ATE Error={ate_error:.6f}")
        
        print("\n" + "="*60)
        print("✅ 多数据集评估功能测试成功!")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_evaluation_consistency():
    """测试评估一致性"""
    print("\n" + "="*60)
    print("测试评估一致性")
    print("="*60)
    
    try:
        # 获取配置
        config = get_config()
        config.x_dim = 38
        
        # 加载数据
        data_loader = load_twins_npz_data(
            data_path='../data28/Twins38.combined.npz',
            train_valid_test=[63, 27, 10],
            seed=123,
            ind=0
        )
        
        # 创建测试模型
        model = create_dummy_model(config)
        
        # 分别评估各数据集
        train_data = data_loader.train
        train_x = train_data['x']
        train_t = train_data['t']
        train_y = train_data['y']
        train_potential_y = np.concatenate([train_data['mu0'], train_data['mu1']], axis=1)
        
        single_train_result = evaluate_model(
            model, train_x, train_t, train_y, train_potential_y, stage='stage1'
        )
        
        # 使用多数据集评估
        multi_results = evaluate_model_on_multiple_datasets(
            model, data_loader, stage='stage1'
        )
        
        # 比较结果
        train_multi_result = multi_results['train']
        
        print("比较单独评估和多数据集评估的训练集结果:")
        for metric in ['pehe', 'ate_error', 'mse_y0', 'mse_y1']:
            single_val = single_train_result[metric]
            multi_val = train_multi_result[metric]
            diff = abs(single_val - multi_val)
            
            print(f"  {metric}:")
            print(f"    单独评估: {single_val:.6f}")
            print(f"    多数据集: {multi_val:.6f}")
            print(f"    差异: {diff:.6f}")
            
            # 允许小的数值误差
            assert diff < 1e-5, f"{metric}结果不一致"
        
        print("✅ 评估一致性测试通过!")
        
    except Exception as e:
        print(f"❌ 一致性测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 运行测试
    success = test_multi_dataset_evaluation()
    
    if success:
        test_evaluation_consistency()
    
    print("\n测试完成!")
