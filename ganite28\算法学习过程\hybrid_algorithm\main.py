"""
主训练脚本：融合算法的完整训练流程
"""

import os
import sys
import numpy as np
import tensorflow as tf
from datetime import datetime
import json

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

from config import get_config
from npzdata import load_twins_npz_data, get_npz_data_info, compute_thresholds
from stage1_trainer import Stage1Trainer
from stage2_trainer import Stage2Trainer
from evaluation import (evaluate_model, print_evaluation_results, save_evaluation_results,
                       evaluate_model_on_multiple_datasets, print_multi_dataset_results,
                       save_multi_dataset_results)

def setup_environment(config):
    """设置环境"""
    # 设置随机种子
    tf.random.set_seed(config.seed)
    np.random.seed(config.seed)
    
    # 设置GPU
    if config.device == 'cuda':
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            try:
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                print(f"Using GPU: {len(gpus)} device(s) found")
            except RuntimeError as e:
                print(f"GPU setup error: {e}")
        else:
            print("No GPU found, using CPU")
            config.device = 'cpu'
    
    # 创建输出目录
    config.create_output_dir()
    
    print(f"Environment setup completed. Device: {config.device}")

def load_and_preprocess_data(config):
    """加载NPZ数据（完全按照DeR-CFR的方式，不做预处理）"""
    print("Loading NPZ data...")

    # 使用NPZ数据加载器，完全按照DeR-CFR的方式
    data_loader = load_twins_npz_data(
        data_path=config.data_path,  # 使用配置中的数据路径
        train_valid_test=[63, 27, 10],  # DeR-CFR的实际划分比例 (63:27:10)
        seed=config.seed,
        ind=0  # 使用第一个重复实验的数据
    )

    # 获取数据信息
    data_info = get_npz_data_info(data_loader)
    print(f"NPZ Data Info:")
    for key, value in data_info.items():
        print(f"  {key}: {value}")

    # 更新配置中的维度信息
    config.x_dim = data_info['x_dim']

    # 计算阈值（完全按照DeR-CFR的方式）
    t_threshold, y_threshold = compute_thresholds(data_loader)
    print(f"Thresholds (DeR-CFR style):")
    print(f"  t_threshold: {t_threshold}")
    print(f"  y_threshold: {y_threshold}")

    # 将阈值添加到配置中
    config.t_threshold = t_threshold
    config.y_threshold = y_threshold

    # 直接使用原始数据，不做预处理（完全按照DeR-CFR）
    train_data = data_loader.train
    valid_data = data_loader.valid
    test_data = data_loader.test

    # 构造潜在结果矩阵（与VGANITE格式兼容）
    train_potential_y = np.concatenate([train_data['mu0'], train_data['mu1']], axis=1)
    valid_potential_y = np.concatenate([valid_data['mu0'], valid_data['mu1']], axis=1)
    test_potential_y = np.concatenate([test_data['mu0'], test_data['mu1']], axis=1)

    # 组织数据格式（与原hybrid_algorithm兼容）
    train_tuple = (
        train_data['x'],      # 特征（原始数据，无预处理）
        train_data['t'],      # 处理变量
        train_data['y'],      # 事实结果
        train_potential_y     # 潜在结果 [mu0, mu1]
    )

    valid_tuple = (
        valid_data['x'],      # 特征（原始数据，无预处理）
        valid_data['t'],      # 处理变量
        valid_data['y'],      # 事实结果
        valid_potential_y     # 潜在结果 [mu0, mu1]
    )

    test_tuple = (
        test_data['x'],       # 特征（原始数据，无预处理）
        test_data['t'],       # 处理变量
        test_data['y'],       # 事实结果
        test_potential_y      # 潜在结果 [mu0, mu1]
    )

    print(f"NPZ data loaded successfully (DeR-CFR style, no preprocessing)!")
    print(f"  Training samples: {len(train_tuple[0])}")
    print(f"  Validation samples: {len(valid_tuple[0])}")
    print(f"  Test samples: {len(test_tuple[0])}")
    print(f"  Feature dimension: {config.x_dim}")
    print(f"  Treatment ratio: {np.mean(train_tuple[1]):.3f}")
    print(f"  Outcome ratio: {np.mean(train_tuple[2]):.3f}")

    return train_tuple, valid_tuple, test_tuple


def load_and_preprocess_data_derc_style(config):
    """
    加载NPZ数据（DeR-CFR风格，返回数据加载器和测试数据）
    """
    print("Loading NPZ data (DeR-CFR style)...")

    # 使用NPZ数据加载器，完全按照DeR-CFR的方式
    data_loader = load_twins_npz_data(
        data_path=config.data_path,  # 使用配置中的数据路径
        train_valid_test=[63, 27, 10],  # DeR-CFR的实际划分比例 (63:27:10)
        seed=config.seed,
        ind=0  # 使用第一个重复实验的数据
    )

    # 获取数据信息
    data_info = get_npz_data_info(data_loader)
    print(f"NPZ Data Info:")
    for key, value in data_info.items():
        print(f"  {key}: {value}")

    # 更新配置中的特征维度
    config.x_dim = data_info['x_dim']

    # 计算阈值（完全按照DeR-CFR的方式）
    t_threshold, y_threshold = compute_thresholds(data_loader)
    config.t_threshold = t_threshold
    config.y_threshold = y_threshold

    print(f"Thresholds (DeR-CFR style):")
    print(f"  t_threshold: {t_threshold}")
    print(f"  y_threshold: {y_threshold}")

    # 准备测试数据（用于评估）
    test_data = data_loader.test
    test_x = test_data['x']
    test_t = test_data['t']
    test_y = test_data['y']
    test_potential_y = np.concatenate([test_data['mu0'], test_data['mu1']], axis=1)

    test_tuple = (test_x, test_t, test_y, test_potential_y)

    print("NPZ data loaded successfully (DeR-CFR style, no preprocessing)!")
    print(f"  Training samples: {data_loader.train_I}")
    print(f"  Validation samples: {data_loader.valid_I}")
    print(f"  Test samples: {data_loader.test_I}")
    print(f"  Feature dimension: {config.x_dim}")
    print(f"  Treatment ratio: {data_info['treatment_ratio']:.3f}")
    print(f"  Outcome ratio: {data_info['outcome_ratio']:.3f}")

    return data_loader, test_tuple

def train_stage1(config, data_loader, test_data):
    """训练阶段一模型（使用DeR-CFR风格的批次生成器）"""
    print("\n" + "="*50)
    print("STAGE 1: DeR-CFR Enhanced Representation Learning + GAN")
    print("="*50)

    # 创建阶段一训练器
    stage1_trainer = Stage1Trainer(config)

    # 训练（传入数据加载器而不是处理后的数据）
    stage1_history = stage1_trainer.train(data_loader, test_data)

    # 在所有数据集上进行最终评估
    print("\n" + "="*60)
    print("STAGE 1: FINAL EVALUATION ON ALL DATASETS")
    print("="*60)

    multi_dataset_results = evaluate_model_on_multiple_datasets(
        stage1_trainer.model, data_loader, stage='stage1'
    )

    # 打印多数据集结果
    print_multi_dataset_results(multi_dataset_results, stage='stage1')

    # 保存多数据集结果
    save_multi_dataset_results(multi_dataset_results, config.output_dir, stage='stage1')

    # 保持向后兼容性：返回测试集结果作为final_results
    final_results = multi_dataset_results['test']

    return stage1_trainer, stage1_history, final_results, multi_dataset_results

def generate_stage1_outputs(stage1_trainer, data_loader, test_data):
    """
    生成阶段一的输出供阶段二使用（适配DeR-CFR风格）
    完全按照VGANITE的wgangp.py第461-470行
    """
    print("\nGenerating Stage 1 outputs for Stage 2...")

    # 从数据加载器获取训练数据
    train_data = data_loader.train
    train_x = train_data['x']
    train_t = train_data['t']
    train_y = train_data['y']

    test_x, test_t, test_y, test_potential_y = test_data

    # 完全按照VGANITE的做法（wgangp.py第461-466行）
    x_train_tf = tf.cast(train_x, tf.float32)
    t_train_tf = tf.cast(train_t, tf.float32)
    y_train_tf = tf.cast(train_y, tf.float32)

    # 获取阶段一的输出logits
    train_outputs = stage1_trainer.model([x_train_tf, t_train_tf, y_train_tf], training=False)
    y_bar_logits_from_stage1 = train_outputs['y_logits']

    # 使用概率作为目标分布（与VGANITE完全一致）
    y_bar_prob_target = tf.nn.sigmoid(y_bar_logits_from_stage1)

    # 同样处理测试集
    x_test_tf = tf.cast(test_x, tf.float32)
    t_test_tf = tf.cast(test_t, tf.float32)
    y_test_tf = tf.cast(test_y, tf.float32)

    test_outputs = stage1_trainer.model([x_test_tf, t_test_tf, y_test_tf], training=False)
    test_y_bar_logits = test_outputs['y_logits']
    test_y_bar_prob = tf.nn.sigmoid(test_y_bar_logits)

    print(f"Stage 1 outputs generated:")
    print(f"  Train y_bar_prob shape: {y_bar_prob_target.shape}")
    print(f"  Test y_bar_prob shape: {test_y_bar_prob.shape}")

    # 返回处理好的数据，供阶段二直接使用
    return {
        'train_x': x_train_tf,
        'train_y_bar_prob': y_bar_prob_target,
        'test_x': x_test_tf,
        'test_y_bar_prob': test_y_bar_prob
    }

def train_stage2(config, stage1_trainer, stage1_outputs):
    """训练阶段二模型"""
    print("\n" + "="*50)
    print("STAGE 2: WGAN-GP Refinement")
    print("="*50)

    # 创建阶段二训练器
    stage2_trainer = Stage2Trainer(config, stage1_trainer.model)

    # 使用阶段一的输出进行真正的阶段二训练
    stage2_history = stage2_trainer.train_with_stage1_outputs(stage1_outputs)

    # 保存模型
    save_path = os.path.join(config.output_dir, 'models', 'stage2_final_model')
    stage2_trainer.save_model(save_path)

    return stage2_trainer, stage2_history

def final_evaluation(config, stage1_trainer, stage2_trainer, data_loader):
    """最终评估（在所有数据集上）"""
    print("\n" + "="*60)
    print("FINAL EVALUATION ON ALL DATASETS")
    print("="*60)

    # 阶段一在所有数据集上的评估
    stage1_multi_results = evaluate_model_on_multiple_datasets(
        stage1_trainer.model, data_loader, stage='stage1'
    )

    # 阶段二在所有数据集上的评估
    stage2_multi_results = evaluate_model_on_multiple_datasets(
        stage2_trainer.model, data_loader, stage='stage2'
    )

    # 打印阶段一结果
    print_multi_dataset_results(stage1_multi_results, stage='stage1')

    # 打印阶段二结果
    print_multi_dataset_results(stage2_multi_results, stage='stage2')

    # 比较两个阶段在测试集上的结果
    stage1_test = stage1_multi_results['test']
    stage2_test = stage2_multi_results['test']

    print("\n" + "="*60)
    print("IMPROVEMENT FROM STAGE 1 TO STAGE 2 (TEST SET)")
    print("="*60)
    print(f"PEHE: {stage1_test['pehe']:.6f} -> {stage2_test['pehe']:.6f} "
          f"({stage2_test['pehe'] - stage1_test['pehe']:+.6f})")
    print(f"ATE Error: {stage1_test['ate_error']:.6f} -> {stage2_test['ate_error']:.6f} "
          f"({stage2_test['ate_error'] - stage1_test['ate_error']:+.6f})")
    print("="*60)

    # 保存多数据集结果
    save_multi_dataset_results(stage1_multi_results, config.output_dir, stage='stage1_final')
    save_multi_dataset_results(stage2_multi_results, config.output_dir, stage='stage2_final')

    return stage1_multi_results, stage2_multi_results

def save_training_summary(config, stage1_history, stage2_history, stage1_multi_results, stage2_multi_results):
    """保存训练总结（包含多数据集结果）"""
    summary = {
        'config': vars(config),
        'stage1_history': stage1_history,
        'stage2_history': stage2_history,
        'final_results': {
            'stage1_multi_datasets': stage1_multi_results,
            'stage2_multi_datasets': stage2_multi_results,
            'stage1_test_only': stage1_multi_results['test'],  # 向后兼容
            'stage2_test_only': stage2_multi_results['test']   # 向后兼容
        },
        'timestamp': datetime.now().isoformat()
    }

    summary_path = os.path.join(config.output_dir, 'training_summary.json')
    with open(summary_path, 'w') as f:
        json.dump(summary, f, indent=2)

    print(f"\nTraining summary saved to {summary_path}")

def main():
    """主函数"""
    print("="*60)
    print("HYBRID ALGORITHM: DeR-CFR + VGANITE")
    print("="*60)
    
    # 获取配置
    config = get_config()
    print(config)
    
    # 设置环境
    setup_environment(config)
    
    # 加载数据（现在返回数据加载器和测试数据）
    data_loader, test_data = load_and_preprocess_data_derc_style(config)

    # 阶段一训练（使用DeR-CFR风格的批次生成器）
    stage1_trainer, stage1_history, stage1_results, stage1_multi_results = train_stage1(config, data_loader, test_data)

    # 生成阶段一输出
    stage1_outputs = generate_stage1_outputs(stage1_trainer, data_loader, test_data)

    # 阶段二训练
    stage2_trainer, stage2_history = train_stage2(config, stage1_trainer, stage1_outputs)

    # 最终评估（在所有数据集上）
    final_stage1_multi_results, final_stage2_multi_results = final_evaluation(
        config, stage1_trainer, stage2_trainer, data_loader
    )
    
    # 保存训练总结
    save_training_summary(config, stage1_history, stage2_history,
                         final_stage1_multi_results, final_stage2_multi_results)
    
    print("\n" + "="*60)
    print("TRAINING COMPLETED SUCCESSFULLY!")
    print("="*60)

if __name__ == "__main__":
    main()
