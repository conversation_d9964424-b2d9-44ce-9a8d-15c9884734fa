整体上 **gA / gI 的可调层数和宽度已经 OK**，
唯一还需补的小细节是在 **RepresentationNetwork** 调用处没有把 `hidden_dim` 设成 `rep_dim`，导致表征网隐藏层的宽度不是固定的 `rep_dim` 而是线性插值。

---

## 1 Representation (I/C/A) 的隐藏层宽度 (`dR`) 没被锁定

### ① 错在哪，为啥错

* 你在 `config.py` 定义了

  ```python
  self.rep_dim    = 100    # dR
  self.rep_layers = 3      # hR
  ```

  按论文语义希望 **I/C/A 的每层都宽 100**。
* 但在 `Stage1Model.__init__` 调 `RepresentationNetwork` 时：

  ```python
  RepresentationNetwork(input_dim=x_dim,
                        output_dim=config.rep_dim,
                        num_layers=config.rep_layers,
                        hidden_dim=None,   # ← default None
                        ...)
  ```

  `hidden_dim=None` 触发了 `networks.py` 里这段代码：

  ```python
  if hidden_dim is None:
      dims = np.linspace(input_dim, output_dim, num_layers+1)
  ```

  于是三层隐藏层的宽度会自动插值得到
  `[x_dim → … → rep_dim]`（如 30 → 53 → 77 → 100），
  **并不是固定 100**。

### ② 正确实现（论文等价写法）

```python
self.rep_I = RepresentationNetwork(
    input_dim=config.x_dim,
    output_dim=config.rep_dim,
    num_layers=config.rep_layers,
    hidden_dim=config.rep_dim,      # 每层都是 rep_dim
    ...
)
```

同理 `rep_C`, `rep_A`。

### ③ 你当前代码 & 修改

| 文件                | 行号    | 现状                | 改成                            |
| ----------------- | ----- | ----------------- | ----------------------------- |
| `stage1_model.py` | 22–38 | `hidden_dim` 参数未传 | 加 `hidden_dim=config.rep_dim` |

---

| 检查点                                                                | 现状                                       |
| ------------------------------------------------------------------ | ---------------------------------------- |
| argparse → `Config.update_from_args`                               | 6 个新参数都在 `get_config()` 中注册，并写回 `Config` |
| gA/gI 名称冲突                                                         | 命名 `gA_h{i}`, `gI_h{i}`，多层不会重名           |
| Generator / Discriminator 仍用旧 `h_dim`, `gen_layers`, `disc_layers` | 不影响 DeR-CFR 的 dY/dT                      |

---

## 核心改动一览（只需这一处）

```diff
@@ class Stage1Model.__init__
- self.rep_I = RepresentationNetwork(
-     input_dim=config.x_dim,
-     output_dim=config.rep_dim,
-     num_layers=config.rep_layers,
-     batch_norm=config.batch_norm,
-     dropout_rate=config.dropout_rate,
-     name='Instrumental')
+ self.rep_I = RepresentationNetwork(
+     input_dim=config.x_dim,
+     output_dim=config.rep_dim,
+     num_layers=config.rep_layers,
+     hidden_dim=config.rep_dim,   # ← 锁定宽度 = rep_dim
+     batch_norm=config.batch_norm,
+     dropout_rate=config.dropout_rate,
+     name='Instrumental')
# 同理修改 rep_C、rep_A！！！
```

---

### 一句话总结

* **I/C/A 表征的 dR 还没锁死**——再把 `hidden_dim=config.rep_dim` 加入 `RepresentationNetwork` 构造即可。
