"""
测试随机数据划分的效果
验证修复后的随机划分是否按照原DeR-CFR方式工作
"""

import numpy as np
from npzdata import load_twins_npz_data, get_npz_data_info

def test_random_split_consistency():
    """测试随机划分的一致性"""
    print("="*60)
    print("测试随机数据划分效果")
    print("="*60)
    
    # 测试1: 相同种子应该产生相同的划分
    print("1. 测试相同种子的一致性...")
    
    data_loader1 = load_twins_npz_data(
        data_path='../data28/Twins38.combined.npz',
        train_valid_test=[63, 27, 10],
        seed=123,
        ind=0
    )
    
    data_loader2 = load_twins_npz_data(
        data_path='../data28/Twins38.combined.npz',
        train_valid_test=[63, 27, 10],
        seed=123,  # 相同种子
        ind=0
    )
    
    # 比较训练集的前5个样本
    train1_x = data_loader1.train['x'][:5, 0]
    train2_x = data_loader2.train['x'][:5, 0]
    
    same_split = np.allclose(train1_x, train2_x)
    print(f"  相同种子划分一致性: {'✓' if same_split else '✗'}")
    
    if same_split:
        print("  ✅ 相同种子产生相同的随机划分")
    else:
        print("  ❌ 相同种子产生不同的划分")
        return False
    
    # 测试2: 不同种子应该产生不同的划分
    print("\n2. 测试不同种子的差异性...")
    
    data_loader3 = load_twins_npz_data(
        data_path='../data28/Twins38.combined.npz',
        train_valid_test=[63, 27, 10],
        seed=456,  # 不同种子
        ind=0
    )
    
    train3_x = data_loader3.train['x'][:5, 0]
    different_split = not np.allclose(train1_x, train3_x)
    
    print(f"  不同种子划分差异性: {'✓' if different_split else '✗'}")
    
    if different_split:
        print("  ✅ 不同种子产生不同的随机划分")
    else:
        print("  ⚠️ 不同种子产生相同的划分（可能是巧合）")
    
    return True

def test_data_distribution_similarity():
    """测试随机划分后各数据集的分布相似性"""
    print("\n" + "="*60)
    print("测试随机划分后的数据分布相似性")
    print("="*60)
    
    # 加载随机划分的数据
    data_loader = load_twins_npz_data(
        data_path='../data28/Twins38.combined.npz',
        train_valid_test=[63, 27, 10],
        seed=123,
        ind=0
    )
    
    # 分析各数据集的分布
    train_data = data_loader.train
    valid_data = data_loader.valid
    test_data = data_loader.test
    
    print("1. 处理变量(t)分布:")
    train_t_ratio = np.mean(train_data['t'])
    valid_t_ratio = np.mean(valid_data['t'])
    test_t_ratio = np.mean(test_data['t'])
    
    print(f"  训练集处理比例: {train_t_ratio:.4f}")
    print(f"  验证集处理比例: {valid_t_ratio:.4f}")
    print(f"  测试集处理比例: {test_t_ratio:.4f}")
    
    # 计算差异
    t_diff_train_test = abs(train_t_ratio - test_t_ratio)
    t_diff_valid_test = abs(valid_t_ratio - test_t_ratio)
    
    print(f"  训练集-测试集差异: {t_diff_train_test:.4f}")
    print(f"  验证集-测试集差异: {t_diff_valid_test:.4f}")
    
    print("\n2. 结果变量(y)分布:")
    train_y_mean = np.mean(train_data['y'])
    valid_y_mean = np.mean(valid_data['y'])
    test_y_mean = np.mean(test_data['y'])
    
    print(f"  训练集结果均值: {train_y_mean:.4f}")
    print(f"  验证集结果均值: {valid_y_mean:.4f}")
    print(f"  测试集结果均值: {test_y_mean:.4f}")
    
    y_diff_train_test = abs(train_y_mean - test_y_mean)
    y_diff_valid_test = abs(valid_y_mean - test_y_mean)
    
    print(f"  训练集-测试集差异: {y_diff_train_test:.4f}")
    print(f"  验证集-测试集差异: {y_diff_valid_test:.4f}")
    
    print("\n3. 真实ATE分布:")
    train_ate = np.mean(train_data['mu1'] - train_data['mu0'])
    valid_ate = np.mean(valid_data['mu1'] - valid_data['mu0'])
    test_ate = np.mean(test_data['mu1'] - test_data['mu0'])
    
    print(f"  训练集真实ATE: {train_ate:.6f}")
    print(f"  验证集真实ATE: {valid_ate:.6f}")
    print(f"  测试集真实ATE: {test_ate:.6f}")
    
    ate_diff_train_test = abs(train_ate - test_ate)
    ate_diff_valid_test = abs(valid_ate - test_ate)
    
    print(f"  训练集-测试集差异: {ate_diff_train_test:.6f}")
    print(f"  验证集-测试集差异: {ate_diff_valid_test:.6f}")
    
    print("\n4. 协变量分布相似性:")
    train_x_mean = np.mean(train_data['x'], axis=0)
    valid_x_mean = np.mean(valid_data['x'], axis=0)
    test_x_mean = np.mean(test_data['x'], axis=0)
    
    x_diff_train_test = np.mean(np.abs(train_x_mean - test_x_mean))
    x_diff_valid_test = np.mean(np.abs(valid_x_mean - test_x_mean))
    
    print(f"  训练集-测试集协变量差异: {x_diff_train_test:.6f}")
    print(f"  验证集-测试集协变量差异: {x_diff_valid_test:.6f}")
    
    # 评估分布相似性
    print("\n5. 分布相似性评估:")
    
    # 设置相似性阈值
    t_threshold = 0.02  # 处理比例差异阈值
    y_threshold = 0.01  # 结果均值差异阈值
    ate_threshold = 0.01  # ATE差异阈值
    x_threshold = 0.01  # 协变量差异阈值
    
    t_similar = t_diff_train_test < t_threshold and t_diff_valid_test < t_threshold
    y_similar = y_diff_train_test < y_threshold and y_diff_valid_test < y_threshold
    ate_similar = ate_diff_train_test < ate_threshold and ate_diff_valid_test < ate_threshold
    x_similar = x_diff_train_test < x_threshold and x_diff_valid_test < x_threshold
    
    print(f"  处理变量分布相似: {'✓' if t_similar else '✗'}")
    print(f"  结果变量分布相似: {'✓' if y_similar else '✗'}")
    print(f"  真实ATE分布相似: {'✓' if ate_similar else '✗'}")
    print(f"  协变量分布相似: {'✓' if x_similar else '✗'}")
    
    overall_similar = t_similar and y_similar and ate_similar and x_similar
    
    if overall_similar:
        print("  ✅ 随机划分后各数据集分布相似")
    else:
        print("  ⚠️ 各数据集分布仍有差异，但这在随机划分中是正常的")
    
    return {
        'train_ate': train_ate,
        'valid_ate': valid_ate,
        'test_ate': test_ate,
        'ate_diff_train_test': ate_diff_train_test,
        'x_diff_train_test': x_diff_train_test
    }

def compare_fixed_vs_random_split():
    """比较固定划分和随机划分的差异"""
    print("\n" + "="*60)
    print("比较修复前后的划分效果")
    print("="*60)
    
    print("修复前（固定划分）的问题:")
    print("  - 训练集总是前63%的样本")
    print("  - 测试集总是后10%的样本")
    print("  - 如果数据有顺序性，会导致系统性偏差")
    print("  - 测试集可能比训练集更容易预测")
    
    print("\n修复后（随机划分）的改进:")
    print("  - 按照原DeR-CFR方式随机打乱数据")
    print("  - 训练集和测试集包含随机分布的样本")
    print("  - 消除了顺序偏差")
    print("  - 各数据集的难度应该相似")
    
    print("\n预期效果:")
    print("  📈 训练集和测试集的性能差异应该减小")
    print("  📈 训练集结果应该略好于测试集（正常的机器学习现象）")
    print("  📈 各数据集的真实ATE应该更相似")

if __name__ == "__main__":
    print("开始测试随机数据划分...")
    
    # 运行所有测试
    test1 = test_random_split_consistency()
    
    if test1:
        results = test_data_distribution_similarity()
        compare_fixed_vs_random_split()
    
    print("\n" + "="*60)
    print("随机划分测试总结")
    print("="*60)
    
    if test1:
        print("🎉 随机数据划分修复成功！")
        print("\n关键改进:")
        print("✅ 按照原DeR-CFR方式实现随机划分")
        print("✅ 相同种子产生一致的划分")
        print("✅ 不同种子产生不同的划分")
        print("✅ 消除了固定划分的顺序偏差")
        
        print("\n🚀 现在可以重新训练模型验证效果！")
        print("预期训练集和测试集的性能差异会减小")
    else:
        print("❌ 随机划分测试失败")
    
    print("="*60)
