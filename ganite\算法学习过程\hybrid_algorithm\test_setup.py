#!/usr/bin/env python3
"""
测试脚本：验证环境配置和代码正确性
"""

import sys
import os
import numpy as np
import traceback

def test_imports():
    """测试必要的包导入"""
    print("测试包导入...")
    
    try:
        import tensorflow as tf
        print(f"✓ TensorFlow {tf.__version__}")
        
        # 测试GPU
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            print(f"✓ 发现 {len(gpus)} 个GPU设备")
        else:
            print("⚠ 未发现GPU设备，将使用CPU")
        
    except ImportError as e:
        print(f"✗ TensorFlow导入失败: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy {np.__version__}")
    except ImportError as e:
        print(f"✗ NumPy导入失败: {e}")
        return False
    
    try:
        import pandas as pd
        print(f"✓ Pandas {pd.__version__}")
    except ImportError as e:
        print(f"✗ Pandas导入失败: {e}")
        return False
    
    try:
        import sklearn
        print(f"✓ Scikit-learn {sklearn.__version__}")
    except ImportError as e:
        print(f"✗ Scikit-learn导入失败: {e}")
        return False
    
    return True

def test_vganite_import():
    """测试VGANITE模块导入"""
    print("\n测试VGANITE模块导入...")
    
    # 添加vganite路径
    vganite_path = os.path.join(os.path.dirname(__file__), '..', 'vganite')
    if os.path.exists(vganite_path):
        sys.path.append(vganite_path)
        print(f"✓ 找到VGANITE路径: {vganite_path}")
        
        try:
            from datasets import data_loading_twin
            print("✓ 成功导入data_loading_twin")
        except ImportError as e:
            print(f"⚠ 无法导入data_loading_twin: {e}")
            print("  将使用备用实现")
        
        try:
            from metrics import PEHE, ATE
            print("✓ 成功导入PEHE, ATE")
        except ImportError as e:
            print(f"⚠ 无法导入PEHE, ATE: {e}")
            print("  将使用备用实现")
    else:
        print(f"⚠ 未找到VGANITE路径: {vganite_path}")
        print("  请确保vganite文件夹在正确位置")

def test_project_structure():
    """测试项目结构"""
    print("\n测试项目结构...")
    
    required_files = [
        'config.py',
        'main.py',
        'data_loader.py',
        'evaluation.py',
        'stage1_trainer.py',
        'models/networks.py',
        'models/stage1_model.py',
        'models/stage2_model.py',
        'environment.yaml'
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = os.path.join(os.path.dirname(__file__), file_path)
        if os.path.exists(full_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n缺失文件: {missing_files}")
        return False
    
    return True

def test_config():
    """测试配置模块"""
    print("\n测试配置模块...")
    
    try:
        from config import Config, get_config
        
        # 测试默认配置
        config = Config()
        print(f"✓ 默认配置创建成功")
        print(f"  数据集: {config.data_name}")
        print(f"  批次大小: {config.batch_size}")
        print(f"  表征维度: {config.rep_dim}")
        
        return True
    except Exception as e:
        print(f"✗ 配置模块测试失败: {e}")
        traceback.print_exc()
        return False

def test_data_loader():
    """测试数据加载模块"""
    print("\n测试数据加载模块...")
    
    try:
        from data_loader import load_data, preprocess_data
        print("✓ 数据加载模块导入成功")
        
        # 创建模拟数据测试预处理
        n_samples = 100
        x_dim = 10
        
        train_x = np.random.randn(n_samples, x_dim)
        train_t = np.random.binomial(1, 0.5, n_samples)
        train_y = np.random.binomial(1, 0.5, n_samples)
        train_potential_y = np.random.randn(n_samples, 2)
        
        test_x = np.random.randn(50, x_dim)
        test_t = np.random.binomial(1, 0.5, 50)
        test_y = np.random.binomial(1, 0.5, 50)
        test_potential_y = np.random.randn(50, 2)
        
        processed_data = preprocess_data(
            train_x, train_t, train_y, train_potential_y,
            test_x, test_t, test_y, test_potential_y
        )
        
        print("✓ 数据预处理测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 数据加载模块测试失败: {e}")
        traceback.print_exc()
        return False

def test_models():
    """测试模型模块"""
    print("\n测试模型模块...")
    
    try:
        from models.networks import RepresentationNetwork, Generator, Discriminator
        from models.stage1_model import Stage1Model
        from config import Config
        
        config = Config()
        config.x_dim = 10
        
        # 测试网络组件
        rep_net = RepresentationNetwork(config.x_dim, config.rep_dim, config.rep_layers)
        print("✓ 表征网络创建成功")
        
        generator = Generator(config.rep_dim * 2, config.h_dim, config.gen_layers)
        print("✓ 生成器创建成功")
        
        discriminator = Discriminator(config.x_dim + 2, config.h_dim, config.disc_layers)
        print("✓ 判别器创建成功")
        
        # 测试阶段一模型
        stage1_model = Stage1Model(config)
        print("✓ 阶段一模型创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型模块测试失败: {e}")
        traceback.print_exc()
        return False

def test_tensorflow_functionality():
    """测试TensorFlow基本功能"""
    print("\n测试TensorFlow基本功能...")
    
    try:
        import tensorflow as tf
        
        # 测试基本运算
        a = tf.constant([1, 2, 3])
        b = tf.constant([4, 5, 6])
        c = tf.add(a, b)
        print(f"✓ 基本运算测试: {c.numpy()}")
        
        # 测试梯度计算
        x = tf.Variable(3.0)
        with tf.GradientTape() as tape:
            y = x ** 2
        dy_dx = tape.gradient(y, x)
        print(f"✓ 梯度计算测试: dy/dx = {dy_dx.numpy()}")
        
        # 测试简单神经网络
        model = tf.keras.Sequential([
            tf.keras.layers.Dense(10, activation='relu', input_shape=(5,)),
            tf.keras.layers.Dense(1)
        ])
        
        x_test = tf.random.normal((32, 5))
        y_test = model(x_test)
        print(f"✓ 神经网络测试: 输出形状 {y_test.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ TensorFlow功能测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("="*60)
    print("混合算法环境测试")
    print("="*60)
    
    tests = [
        ("包导入", test_imports),
        ("VGANITE模块", test_vganite_import),
        ("项目结构", test_project_structure),
        ("配置模块", test_config),
        ("数据加载", test_data_loader),
        ("模型模块", test_models),
        ("TensorFlow功能", test_tensorflow_functionality)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试出现异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！环境配置正确，可以开始训练。")
        print("\n运行命令:")
        print("  python main.py --data_name twin --stage1_epochs 100 --stage2_epochs 50")
    else:
        print(f"\n⚠ 有 {len(results) - passed} 项测试失败，请检查环境配置。")
        print("\n建议:")
        print("1. 检查Python包安装: pip install -r requirements.txt")
        print("2. 确保VGANITE文件夹在正确位置")
        print("3. 检查CUDA和GPU驱动安装")

if __name__ == "__main__":
    main()
