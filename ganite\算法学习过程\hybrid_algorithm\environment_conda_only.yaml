# 纯conda环境配置 - 完全避免conda/pip混用
# 注意：某些TensorFlow版本可能在conda-forge中不可用

name: hybrid_algorithm_conda
channels:
  - conda-forge
  - defaults
dependencies:
  # Python版本
  - python=3.8
  
  # 核心科学计算包
  - numpy=1.21.0
  - scipy=1.8.0
  - pandas=1.4.0
  - scikit-learn=1.0.2
  
  # 深度学习框架（通过conda安装）
  - tensorflow=2.8.0
  - tensorflow-probability=0.15.0
  
  # 可视化包
  - matplotlib=3.5.0
  - seaborn=0.11.0
  
  # 工具包
  - tqdm=4.64.0
  - jupyter=1.0.0
