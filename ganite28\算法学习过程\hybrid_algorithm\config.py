"""
配置文件：定义融合算法的所有超参数和设置
"""

import argparse
import os

class Config:
    """融合算法配置类"""
    
    def __init__(self):
        # 数据相关参数
        self.data_name = 'twins'  # 数据集名称
        self.data_path = '../data28/Twins38.combined.npz'  # NPZ数据路径
        self.x_dim = 30  # 输入特征维度（twin数据集）
        self.output_dir = './results'  # 输出目录
        
        # 训练相关参数
        self.batch_size = 128  # 批次大小
        self.stage1_epochs = 250  # 阶段一训练轮数（避免过度收敛）
        self.stage2_epochs = 100   # 阶段二训练轮数
        self.learning_rate = 0.0001  # 主网络学习率（表征网络+生成器）
        self.stage1_disc_lr = 0.0001  # 阶段一判别器学习率
        self.stage2_lr = 0.0001    # 阶段二学习率
        
        # 网络架构参数（按照Twins数据集的超参数设置）
        # ------ representation (I/C/A) ------
        self.rep_dim = 7       # dR: I/C/A 表征隐藏层宽度 (Twins设置)
        self.rep_layers = 7    # hR: I/C/A 表征隐藏层层数 (Twins设置)

        # ------ gA (A -> Y) ------
        self.gA_dim = 64       # dY: gA (A → Y) 隐藏层宽度 (Twins设置)
        self.gA_layers = 64    # hY: gA 隐藏层层数 (Twins设置)

        # ------ gI (I -> T) ------
        self.gI_dim = 64       # dT: gI (I → T) 隐藏层宽度 (Twins设置)
        self.gI_layers = 64    # hT: gI 隐藏层层数 (Twins设置)

        # ------ 其他网络参数 ------
        self.h_dim = 64        # 隐藏层维度
        self.gen_layers = 3    # 生成器层数
        self.disc_layers = 3   # 判别器层数
        
        # DeR-CFR损失函数权重（按照Twins数据集设置）
        self.p_alpha = 1e-2    # 调整变量损失权重 (α)
        self.p_beta = 1e-3     # 工具变量损失权重 (β)
        self.p_mu = 1e-4       # 正交正则化权重 (μ) - 降低以防止过度约束
        self.p_gamma = 1       # 平衡损失权重 (γ) - 降低以防止过度平衡
        self.p_lambda = 0.01   # 权重衰减 (λ) - 极大降低以防止过度正则化
        self.p_adv = 0.01      # 对抗损失权重（GAN部分）- 极大降低以防止模式崩塌

        # DeR-CFR特征贡献度计算参数
        self.select_layer = 0  # 贡献度计算层数（0表示使用所有层）
        
        # WGAN-GP参数
        self.lambda_gp = 10.0  # 梯度惩罚权重
        self.n_critic = 5      # 判别器更新次数
        self.lambda_supervised = 50.0  # 监督损失权重
        
        # VAE相关参数
        self.kl_weight = 1e-5  # KL散度权重
        self.recon_weight = 1.0  # 重建损失权重
        
        # 训练设置（按照Twins数据集设置）
        self.use_p_correction = True   # 是否使用倾向性得分校正
        self.reweight_sample = False   # 是否重新加权样本（实验B：训练时禁用）
        self.batch_norm = True         # 是否使用批归一化 (Twins: True)
        self.rep_normalization = True  # 是否归一化表征 (Twins: True)
        self.dropout_rate = 0.05       # Dropout率 - 降低以防止过度正则化
        
        # 评估设置
        self.eval_freq = 100   # 评估频率
        self.save_freq = 500   # 保存频率
        self.early_stop_patience = 100  # 早停耐心值
        
        # 随机种子
        self.seed = 3407

        # DeR-CFR阈值参数（在数据加载时设置）
        self.t_threshold = 0.5  # 处理变量阈值
        self.y_threshold = None  # 结果变量阈值（数据加载时计算）
        
        # 设备设置
        self.device = 'cuda'  # 'cuda' 或 'cpu'
        
        # 日志设置
        self.log_level = 'INFO'
        self.log_file = 'training.log'
        
    def update_from_args(self, args):
        """从命令行参数更新配置"""
        for key, value in vars(args).items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def create_output_dir(self):
        """创建输出目录"""
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'models'), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'logs'), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'figures'), exist_ok=True)
    
    def __str__(self):
        """打印配置信息"""
        config_str = "=== 融合算法配置 ===\n"
        for key, value in self.__dict__.items():
            config_str += f"{key}: {value}\n"
        return config_str

def get_config():
    """获取配置对象"""
    parser = argparse.ArgumentParser(description='DeR-CFR-VGANITE 融合算法')
    
    # 数据参数
    parser.add_argument('--data_name', type=str, default='twins',
                       choices=['synthetic', 'ihdp', 'twins', 'twin'],
                       help='数据集名称')
    parser.add_argument('--data_path', type=str, default='../data28/Twins38.combined.npz',
                       help='数据路径')
    parser.add_argument('--x_dim', type=int, default=38,
                       help='输入特征维度')
    parser.add_argument('--output_dir', type=str, default='./results',
                       help='输出目录')
    
    # 训练参数
    parser.add_argument('--batch_size', type=int, default=5271,
                       help='批次大小')
    parser.add_argument('--stage1_epochs', type=int, default=1000,
                       help='阶段一训练轮数')
    parser.add_argument('--stage2_epochs', type=int, default=500,
                       help='阶段二训练轮数')
    parser.add_argument('--learning_rate', type=float, default=0.0004,
                       help='主网络学习率（表征网络+生成器）')
    parser.add_argument('--stage1_disc_lr', type=float, default=1e-4,
                       help='阶段一判别器学习率')
    parser.add_argument('--stage2_lr', type=float, default=0.0001,
                       help='阶段二学习率')
    
    # 网络架构参数（Twins数据集设置）
    # ------ representation (I/C/A) ------
    parser.add_argument('--rep_dim', type=int, default=7,
                       help='dR: I/C/A 表征隐藏层宽度 (Twins: 7)')
    parser.add_argument('--rep_layers', type=int, default=7,
                       help='hR: I/C/A 表征隐藏层层数 (Twins: 7)')

    # ------ gA (A -> Y) ------
    parser.add_argument('--gA_dim', type=int, default=64,
                       help='dY: gA (A → Y) 隐藏层宽度 (Twins: 64)')
    parser.add_argument('--gA_layers', type=int, default=64,
                       help='hY: gA 隐藏层层数 (Twins: 64)')

    # ------ gI (I -> T) ------
    parser.add_argument('--gI_dim', type=int, default=64,
                       help='dT: gI (I → T) 隐藏层宽度 (Twins: 64)')
    parser.add_argument('--gI_layers', type=int, default=64,
                       help='hT: gI 隐藏层层数 (Twins: 64)')

    # ------ 其他网络参数 ------
    parser.add_argument('--h_dim', type=int, default=64,
                       help='隐藏层维度')
    
    # 损失函数权重（Twins数据集设置）
    parser.add_argument('--p_alpha', type=float, default=1e-2,
                       help='调整变量损失权重 (α, Twins: 1e-2)')
    parser.add_argument('--p_beta', type=float, default=1e-3,
                       help='工具变量损失权重 (β, Twins: 1e-3)')
    parser.add_argument('--p_mu', type=float, default=1e-3,
                       help='正交正则化权重 (μ, Twins: 1e-3)')
    parser.add_argument('--p_gamma', type=float, default=5,
                       help='平衡损失权重 (γ, Twins: 5)')
    parser.add_argument('--p_adv', type=float, default=4.0,
                       help='对抗损失权重（GAN部分）')
    parser.add_argument('--p_lambda', type=float, default=5,
                       help='权重衰减 (λ, Twins: 5)')
    parser.add_argument('--select_layer', type=int, default=0,
                       help='贡献度计算层数（0表示使用所有层）')


    # WGAN-GP参数
    parser.add_argument('--lambda_gp', type=float, default=10.0,
                       help='梯度惩罚权重')
    parser.add_argument('--n_critic', type=int, default=5,
                       help='判别器更新次数')
    parser.add_argument('--lambda_supervised', type=float, default=10.0,
                       help='监督损失权重')
    
    # 训练设置
    parser.add_argument('--eval_freq', type=int, default=100,
                       help='评估频率')
    parser.add_argument('--save_freq', type=int, default=500,
                       help='保存频率')

    # 其他设置
    parser.add_argument('--seed', type=int, default=42,
                       help='随机种子')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cuda', 'cpu'],
                       help='计算设备')
    
    args = parser.parse_args()
    
    config = Config()
    config.update_from_args(args)
    config.create_output_dir()
    
    return config

if __name__ == '__main__':
    config = get_config()
    print(config)
