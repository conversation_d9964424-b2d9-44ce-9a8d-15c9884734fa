# 安全的环境配置 - 避免conda/pip混用冲突
# 所有科学计算包都通过conda安装，只有TensorFlow通过pip安装

name: hybrid_algorithm
channels:
  - conda-forge
  - defaults
dependencies:
  # Python版本
  - python=3.8
  
  # 核心科学计算包（通过conda安装以确保二进制兼容性）
  - numpy=1.21.0
  - scipy=1.8.0
  - pandas=1.4.0
  - scikit-learn=1.0.2
  
  # 可视化包
  - matplotlib=3.5.0
  - seaborn=0.11.0
  
  # 工具包
  - tqdm=4.64.0
  - jupyter=1.0.0
  
  # pip（用于安装TensorFlow）
  - pip
  
  # 只通过pip安装TensorFlow相关包
  - pip:
    - tensorflow==2.8.0
    - tensorflow-probability==0.15.0
