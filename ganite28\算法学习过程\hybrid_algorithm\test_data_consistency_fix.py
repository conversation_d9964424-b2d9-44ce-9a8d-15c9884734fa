"""
测试数据顺序一致性修复效果
验证训练时和评估时使用相同的数据顺序
"""

import numpy as np
import tensorflow as tf
from npzdata import load_twins_npz_data, create_derc_batch_generator
from evaluation import evaluate_model_on_multiple_datasets
from models.stage1_model import Stage1Model
from config import get_config

def test_data_consistency_fix():
    """测试数据顺序一致性修复"""
    print("="*60)
    print("测试数据顺序一致性修复")
    print("="*60)
    
    try:
        # 获取配置
        config = get_config()
        config.x_dim = 38
        
        # 加载数据
        print("1. 加载数据...")
        data_loader = load_twins_npz_data(
            data_path='../data28/Twins38.combined.npz',
            train_valid_test=[63, 27, 10],
            seed=123,
            ind=0
        )
        
        # 创建批次生成器
        print("\n2. 创建批次生成器...")
        batch_generator = create_derc_batch_generator(data_loader, config.batch_size)
        
        print(f"批次生成器配置:")
        print(f"  数据打乱: {batch_generator.shuffle_}")
        print(f"  批次大小: {batch_generator.batch_size}")
        print(f"  每epoch批次数: {batch_generator.batch_num}")
        
        # 验证数据顺序一致性
        print("\n3. 验证数据顺序一致性...")
        
        # 获取原始训练数据
        original_train_x = data_loader.train['x']
        original_train_indices = data_loader.train['I']
        
        print(f"原始训练数据:")
        print(f"  前5个样本的x[0]: {original_train_x[:5, 0]}")
        print(f"  前5个样本的索引: {original_train_indices[:5]}")
        
        # 获取批次数据
        x, t, y, I, ycf, mu0, mu1 = batch_generator.batch.__next__()
        
        print(f"\n批次数据:")
        print(f"  前5个样本的x[0]: {x[:5, 0]}")
        print(f"  前5个样本的索引: {I[:5]}")
        
        # 验证数据是否一致
        x_consistent = np.allclose(original_train_x[:5, 0], x[:5, 0])
        indices_consistent = np.array_equal(original_train_indices[:5], I[:5])
        
        print(f"\n数据一致性检查:")
        print(f"  特征数据一致: {'✓' if x_consistent else '✗'}")
        print(f"  索引数据一致: {'✓' if indices_consistent else '✗'}")
        
        if x_consistent and indices_consistent:
            print("  ✅ 数据顺序一致性修复成功！")
        else:
            print("  ❌ 数据顺序仍有问题")
            return False
        
        # 测试连续批次的一致性
        print("\n4. 测试连续批次的一致性...")
        x2, t2, y2, I2, ycf2, mu02, mu12 = batch_generator.batch.__next__()
        
        # 在禁用打乱的情况下，连续批次应该相同（全批次训练）
        x_same = np.allclose(x[:5, 0], x2[:5, 0])
        indices_same = np.array_equal(I[:5], I2[:5])
        
        print(f"连续批次一致性:")
        print(f"  特征数据相同: {'✓' if x_same else '✗'}")
        print(f"  索引数据相同: {'✓' if indices_same else '✗'}")
        
        if x_same and indices_same:
            print("  ✅ 连续批次数据一致（符合全批次训练预期）")
        else:
            print("  ⚠️ 连续批次数据不同（可能仍有打乱）")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sample_weight_consistency():
    """测试样本权重一致性"""
    print("\n" + "="*60)
    print("测试样本权重一致性")
    print("="*60)
    
    try:
        # 获取配置
        config = get_config()
        config.x_dim = 38
        
        # 加载数据
        data_loader = load_twins_npz_data(
            data_path='../data28/Twins38.combined.npz',
            train_valid_test=[63, 27, 10],
            seed=123,
            ind=0
        )
        
        # 创建模型
        print("1. 创建模型并初始化样本权重...")
        model = Stage1Model(config)
        model.build_sample_weights(data_loader.num)
        
        # 模拟权重更新
        print("\n2. 模拟样本权重更新...")
        
        # 获取训练数据的前10个样本
        train_indices = data_loader.train['I'][:10]
        print(f"前10个训练样本索引: {train_indices}")
        
        # 模拟更新这些样本的权重
        new_weights = np.ones((data_loader.num, 1), dtype=np.float32)
        new_weights[train_indices] = 2.0  # 将前10个样本权重设为2.0
        model.sample_weights.assign(new_weights)
        
        # 验证权重更新
        updated_weights = tf.gather(model.sample_weights, train_indices)
        print(f"更新后的权重: {updated_weights.numpy().flatten()}")
        
        # 获取批次数据
        batch_generator = create_derc_batch_generator(data_loader, config.batch_size)
        x, t, y, I, ycf, mu0, mu1 = batch_generator.batch.__next__()
        
        # 验证批次中的索引与权重匹配
        batch_weights = tf.gather(model.sample_weights, I[:10])
        print(f"批次中前10个样本的权重: {batch_weights.numpy().flatten()}")
        
        # 检查权重是否匹配
        weights_match = np.allclose(updated_weights.numpy().flatten(), batch_weights.numpy().flatten())
        
        print(f"\n权重匹配检查:")
        print(f"  样本权重与索引匹配: {'✓' if weights_match else '✗'}")
        
        if weights_match:
            print("  ✅ 样本权重与实际样本正确匹配！")
        else:
            print("  ❌ 样本权重与实际样本不匹配")
            return False
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_evaluation_consistency():
    """测试训练和评估的数据一致性"""
    print("\n" + "="*60)
    print("测试训练和评估的数据一致性")
    print("="*60)
    
    try:
        # 获取配置
        config = get_config()
        config.x_dim = 38
        
        # 加载数据
        data_loader = load_twins_npz_data(
            data_path='../data28/Twins38.combined.npz',
            train_valid_test=[63, 27, 10],
            seed=123,
            ind=0
        )
        
        print("1. 比较训练数据和评估数据...")
        
        # 训练时使用的数据（通过批次生成器）
        batch_generator = create_derc_batch_generator(data_loader, config.batch_size)
        train_x_batch, train_t_batch, train_y_batch, train_I_batch, _, _, _ = batch_generator.batch.__next__()
        
        # 评估时使用的数据（直接从data_loader）
        eval_train_x = np.array(data_loader.train['x'], dtype=np.float32)
        eval_train_t = np.array(data_loader.train['t'], dtype=np.float32)
        eval_train_y = np.array(data_loader.train['y'], dtype=np.float32)
        eval_train_I = data_loader.train['I']
        
        print(f"训练时数据 (前5个样本):")
        print(f"  x[0]: {train_x_batch[:5, 0]}")
        print(f"  t: {train_t_batch[:5].flatten()}")
        print(f"  索引: {train_I_batch[:5]}")
        
        print(f"\n评估时数据 (前5个样本):")
        print(f"  x[0]: {eval_train_x[:5, 0]}")
        print(f"  t: {eval_train_t[:5].flatten()}")
        print(f"  索引: {eval_train_I[:5]}")
        
        # 检查数据一致性
        x_consistent = np.allclose(train_x_batch[:5, 0], eval_train_x[:5, 0])
        t_consistent = np.allclose(train_t_batch[:5], eval_train_t[:5])
        indices_consistent = np.array_equal(train_I_batch[:5], eval_train_I[:5])
        
        print(f"\n训练-评估数据一致性:")
        print(f"  特征x一致: {'✓' if x_consistent else '✗'}")
        print(f"  处理t一致: {'✓' if t_consistent else '✗'}")
        print(f"  索引I一致: {'✓' if indices_consistent else '✗'}")
        
        if x_consistent and t_consistent and indices_consistent:
            print("  ✅ 训练和评估使用相同的数据顺序！")
            print("  ✅ 修复成功：消除了数据顺序不一致问题！")
        else:
            print("  ❌ 训练和评估数据顺序仍不一致")
            return False
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试数据顺序一致性修复效果...")
    
    # 运行所有测试
    test1 = test_data_consistency_fix()
    test2 = test_sample_weight_consistency() if test1 else False
    test3 = test_training_evaluation_consistency() if test2 else False
    
    print("\n" + "="*60)
    print("修复效果总结")
    print("="*60)
    
    if test1 and test2 and test3:
        print("🎉 数据顺序一致性修复成功！")
        print("\n关键改进:")
        print("✅ 禁用了训练时的数据打乱")
        print("✅ 样本权重与实际样本正确匹配")
        print("✅ 训练和评估使用相同的数据顺序")
        print("✅ 消除了数据泄露问题")
        print("\n预期效果:")
        print("📈 训练集评估结果将显著改善")
        print("📈 训练集PEHE应该小于测试集PEHE")
        print("📈 样本权重机制将正常工作")
        
        print("\n🚀 现在可以重新训练模型验证修复效果！")
    else:
        print("❌ 修复测试失败，需要进一步检查")
    
    print("="*60)
