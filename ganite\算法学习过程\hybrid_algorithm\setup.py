#!/usr/bin/env python3
"""
安装脚本：自动配置环境和依赖
"""

import os
import sys
import subprocess
import platform

def run_command(cmd, check=True):
    """运行命令"""
    print(f"执行: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if result.returncode != 0 and check:
        print(f"错误: {result.stderr}")
        return False
    
    if result.stdout:
        print(result.stdout)
    
    return True

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3 or version.minor < 8:
        print("错误: 需要Python 3.8或更高版本")
        return False
    
    return True

def check_conda():
    """检查Conda是否可用"""
    try:
        result = subprocess.run(['conda', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"Conda可用: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("Conda不可用，将使用pip安装")
    return False

def install_with_conda():
    """使用Conda安装环境"""
    print("使用Conda创建环境...")

    # 选择环境配置文件
    print("\n选择环境配置:")
    print("1. 安全配置 (推荐) - 科学计算包用conda，TensorFlow用pip")
    print("2. 纯conda配置 - 所有包都用conda (可能某些版本不可用)")
    print("3. 默认配置 - 原始配置文件")

    choice = input("请选择 (1/2/3): ").strip()

    if choice == '1':
        env_file = 'environment_safe.yaml'
        env_name = 'hybrid_algorithm'
    elif choice == '2':
        env_file = 'environment_conda_only.yaml'
        env_name = 'hybrid_algorithm_conda'
    else:
        env_file = 'environment.yaml'
        env_name = 'hybrid_algorithm'

    print(f"使用配置文件: {env_file}")

    # 检查环境是否已存在
    result = subprocess.run(['conda', 'env', 'list'], capture_output=True, text=True)
    if env_name in result.stdout:
        print(f"环境{env_name}已存在")
        choice = input("是否删除并重新创建? (y/n): ")
        if choice.lower() == 'y':
            run_command(f'conda env remove -n {env_name}')
        else:
            print("跳过环境创建")
            return True

    # 创建环境
    if not run_command(f'conda env create -f {env_file}'):
        print(f"使用{env_file}创建环境失败")
        if env_file != 'environment.yaml':
            print("尝试使用默认配置...")
            if not run_command('conda env create -f environment.yaml'):
                return False
            env_name = 'hybrid_algorithm'
        else:
            return False

    print(f"\n环境创建成功！")
    print(f"激活环境: conda activate {env_name}")
    return True

def install_with_pip():
    """使用pip安装依赖"""
    print("使用pip安装依赖...")
    print("⚠️  警告: 使用pip安装科学计算包可能导致二进制兼容性问题")
    print("   建议使用conda安装以获得更好的稳定性")

    choice = input("是否继续使用pip安装? (y/n): ")
    if choice.lower() != 'y':
        print("安装取消")
        return False

    # 升级pip
    run_command(f'{sys.executable} -m pip install --upgrade pip')

    # 分步安装，先安装科学计算包
    print("安装科学计算包...")
    scientific_packages = [
        'numpy>=1.21.0',
        'scipy>=1.8.0',
        'pandas>=1.4.0',
        'scikit-learn>=1.0.2',
        'matplotlib>=3.5.0',
        'seaborn>=0.11.0',
        'tqdm>=4.64.0'
    ]

    for package in scientific_packages:
        if not run_command(f'{sys.executable} -m pip install "{package}"'):
            print(f"安装{package}失败")
            return False

    # 安装TensorFlow
    print("安装TensorFlow...")
    tf_packages = [
        'tensorflow==2.8.0',
        'tensorflow-probability==0.15.0'
    ]

    for package in tf_packages:
        if not run_command(f'{sys.executable} -m pip install "{package}"'):
            print(f"安装{package}失败")
            return False

    print("依赖安装成功！")
    return True

def check_gpu():
    """检查GPU支持"""
    try:
        import tensorflow as tf
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            print(f"发现 {len(gpus)} 个GPU设备")
            for i, gpu in enumerate(gpus):
                print(f"  GPU {i}: {gpu.name}")
            return True
        else:
            print("未发现GPU设备")
            return False
    except ImportError:
        print("TensorFlow未安装，无法检查GPU")
        return False

def test_installation():
    """测试安装"""
    print("\n测试安装...")
    
    try:
        # 测试基本导入
        import tensorflow as tf
        import numpy as np
        import pandas as pd
        import sklearn
        
        print("✓ 基本包导入成功")
        
        # 测试TensorFlow
        print(f"✓ TensorFlow版本: {tf.__version__}")
        
        # 测试GPU
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            print(f"✓ GPU支持: {len(gpus)} 个设备")
        else:
            print("⚠ 无GPU支持，将使用CPU")
        
        # 测试项目模块
        sys.path.append(os.path.dirname(__file__))
        from config import Config
        print("✓ 项目配置模块导入成功")
        
        print("\n🎉 安装测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 安装测试失败: {e}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("混合算法环境安装脚本")
    print("="*60)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 检查操作系统
    os_name = platform.system()
    print(f"操作系统: {os_name}")
    
    # 选择安装方式
    use_conda = check_conda()
    
    if use_conda:
        choice = input("使用Conda安装? (推荐) (y/n): ")
        if choice.lower() != 'y':
            use_conda = False
    
    # 安装
    success = False
    if use_conda:
        success = install_with_conda()
    else:
        success = install_with_pip()
    
    if not success:
        print("安装失败！")
        return
    
    # 测试安装
    if not use_conda:  # 如果使用conda，需要先激活环境
        test_installation()
    
    # 提供使用说明
    print("\n" + "="*60)
    print("安装完成！")
    print("="*60)
    
    if use_conda:
        print("下一步:")
        print("1. 激活环境: conda activate hybrid_algorithm")
        print("2. 测试安装: python test_setup.py")
        print("3. 运行实验: python main.py --data_name twin")
    else:
        print("下一步:")
        print("1. 测试安装: python test_setup.py")
        print("2. 运行实验: python main.py --data_name twin")
    
    print("\n快速测试命令:")
    print("python run_experiment.py --quick_test")

if __name__ == "__main__":
    main()
