#!/usr/bin/env python3
"""
测试select_layer参数是否真的起作用
"""

import os
import sys
import tensorflow as tf
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from models.stage1_model import Stage1Model

def test_select_layer_effect():
    """测试select_layer参数的实际效果"""
    print("="*60)
    print("测试select_layer参数效果")
    print("="*60)
    
    # 创建配置
    config = Config()
    config.x_dim = 10
    config.rep_dim = 8
    config.rep_layers = 3  # 3层网络
    config.batch_norm = False  # 简化测试
    config.dropout_rate = 0.0  # 简化测试
    
    # 创建模型
    model = Stage1Model(config)
    
    # 创建测试数据
    batch_size = 32
    x = tf.random.normal([batch_size, config.x_dim])
    t = tf.random.uniform([batch_size, 1], maxval=2, dtype=tf.int32)
    t = tf.cast(t, tf.float32)
    y = tf.random.uniform([batch_size, 1], maxval=2, dtype=tf.int32)
    y = tf.cast(y, tf.float32)
    
    # 初始化模型
    _ = model([x, t, y], training=False)
    
    print(f"网络配置:")
    print(f"  - 输入维度: {config.x_dim}")
    print(f"  - 表征维度: {config.rep_dim}")
    print(f"  - 网络层数: {config.rep_layers}")
    
    # 获取权重信息
    w_I = model.rep_I.get_weights_for_orthogonal_loss()
    w_C = model.rep_C.get_weights_for_orthogonal_loss()
    w_A = model.rep_A.get_weights_for_orthogonal_loss()
    
    print(f"\n权重矩阵信息:")
    print(f"  - I网络权重层数: {len(w_I)}")
    print(f"  - C网络权重层数: {len(w_C)}")
    print(f"  - A网络权重层数: {len(w_A)}")
    
    for i, w in enumerate(w_I):
        print(f"  - I网络第{i}层权重形状: {w.shape}")
    
    # 测试不同select_layer值的效果
    test_cases = [0, 1, 2, 3, 999]  # 包括超出范围的值
    
    for select_layer in test_cases:
        print(f"\n{'='*40}")
        print(f"测试 select_layer = {select_layer}")
        print(f"{'='*40}")
        
        # 临时修改配置
        original_select_layer = config.select_layer
        config.select_layer = select_layer
        
        try:
            # 计算正交损失
            orthogonal_loss = model.compute_orthogonal_loss()
            print(f"✓ 正交损失计算成功: {orthogonal_loss.numpy():.6f}")
            
            # 手动验证权重矩阵连乘逻辑
            if config.select_layer == 0:
                layer_num = len(w_I)
                print(f"  使用所有层: {layer_num} 层")
            else:
                layer_num = min(config.select_layer, len(w_I))
                print(f"  使用指定层数: {layer_num} 层 (原始值: {config.select_layer})")
            
            if layer_num > 0:
                # 手动计算权重连乘
                w_I_sum_manual = w_I[0]
                for i in range(1, layer_num):
                    w_I_sum_manual = tf.matmul(w_I_sum_manual, w_I[i])
                
                print(f"  连乘后权重矩阵形状: {w_I_sum_manual.shape}")
                print(f"  连乘后权重矩阵范数: {tf.norm(w_I_sum_manual).numpy():.6f}")
                
                # 计算特征贡献度
                w_I_mean_manual = tf.reduce_mean(tf.abs(w_I_sum_manual), axis=1)
                print(f"  特征贡献度向量形状: {w_I_mean_manual.shape}")
                print(f"  特征贡献度向量前5个值: {w_I_mean_manual.numpy()[:5]}")
            else:
                print(f"  没有使用任何层")
                
        except Exception as e:
            print(f"✗ 计算失败: {e}")
        
        # 恢复原始配置
        config.select_layer = original_select_layer

def test_weight_matrix_multiplication():
    """详细测试权重矩阵连乘过程"""
    print("\n" + "="*60)
    print("详细测试权重矩阵连乘过程")
    print("="*60)
    
    # 创建简单的测试权重矩阵
    w1 = tf.constant([[1.0, 2.0], [3.0, 4.0]], dtype=tf.float32)  # 2x2
    w2 = tf.constant([[0.5, 1.5], [2.5, 3.5]], dtype=tf.float32)  # 2x2
    w3 = tf.constant([[1.1, 0.9], [0.8, 1.2]], dtype=tf.float32)  # 2x2
    
    weights = [w1, w2, w3]
    
    print("测试权重矩阵:")
    for i, w in enumerate(weights):
        print(f"  W{i+1} = \n{w.numpy()}")
    
    # 测试不同层数的连乘
    for layer_num in [1, 2, 3]:
        print(f"\n使用前 {layer_num} 层:")
        
        w_sum = weights[0]
        print(f"  初始: W_sum = W1")
        
        for i in range(1, layer_num):
            w_sum = tf.matmul(w_sum, weights[i])
            print(f"  第{i+1}步: W_sum = W_sum @ W{i+1}")
        
        print(f"  最终结果:\n{w_sum.numpy()}")
        
        # 计算特征贡献度
        w_mean = tf.reduce_mean(tf.abs(w_sum), axis=1)
        print(f"  特征贡献度: {w_mean.numpy()}")

if __name__ == '__main__':
    # 设置TensorFlow日志级别
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
    
    test_select_layer_effect()
    test_weight_matrix_multiplication()
    
    print("\n" + "="*60)
    print("测试完成")
    print("="*60)
