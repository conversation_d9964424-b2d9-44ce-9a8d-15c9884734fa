"""
测试NPZ数据加载功能
验证新的数据加载方式是否正常工作
"""

import os
import sys
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

from npzdata import load_twins_npz_data, get_npz_data_info, compute_thresholds
from config import get_config

def test_npz_data_loading():
    """测试NPZ数据加载"""
    print("="*60)
    print("测试NPZ数据加载功能")
    print("="*60)
    
    try:
        # 获取配置
        config = get_config()
        print(f"数据路径: {config.data_path}")
        print(f"随机种子: {config.seed}")
        
        # 检查数据文件是否存在
        if not os.path.exists(config.data_path):
            print(f"❌ 数据文件不存在: {config.data_path}")
            print("请确保数据文件路径正确")
            return False
        
        print(f"✅ 数据文件存在: {config.data_path}")
        
        # 加载NPZ数据
        print("\n1. 加载NPZ数据...")
        data_loader = load_twins_npz_data(
            data_path=config.data_path,
            train_valid_test=[63, 27, 10],  # DeR-CFR的实际划分比例
            seed=config.seed,
            ind=0
        )
        
        # 获取数据信息
        print("\n2. 获取数据信息...")
        data_info = get_npz_data_info(data_loader)
        
        print("数据集信息:")
        for key, value in data_info.items():
            if isinstance(value, (int, float)):
                print(f"  {key}: {value}")
            else:
                print(f"  {key}: {type(value)} (shape: {getattr(value, 'shape', 'N/A')})")
        
        # 验证数据格式
        print("\n3. 验证数据格式...")
        
        # 检查训练集
        train_data = data_loader.train
        print("训练集变量:")
        for key, value in train_data.items():
            print(f"  {key}: {value.shape} (dtype: {value.dtype})")
        
        # 检查验证集
        valid_data = data_loader.valid
        print("验证集变量:")
        for key, value in valid_data.items():
            print(f"  {key}: {value.shape} (dtype: {value.dtype})")
        
        # 检查测试集
        test_data = data_loader.test
        print("测试集变量:")
        for key, value in test_data.items():
            print(f"  {key}: {value.shape} (dtype: {value.dtype})")
        
        # 验证数据一致性
        print("\n4. 验证数据一致性...")
        
        # 检查特征维度一致性
        x_dims = [train_data['x'].shape[1], valid_data['x'].shape[1], test_data['x'].shape[1]]
        if len(set(x_dims)) == 1:
            print(f"✅ 特征维度一致: {x_dims[0]}")
        else:
            print(f"❌ 特征维度不一致: {x_dims}")
            return False
        
        # 检查数据范围
        print("\n5. 检查数据范围...")
        print(f"训练集处理变量分布: {np.mean(train_data['t']):.3f}")
        print(f"训练集结果分布: {np.mean(train_data['y']):.3f}")
        print(f"训练集特征范围: [{np.min(train_data['x']):.3f}, {np.max(train_data['x']):.3f}]")
        
        # 测试阈值计算（DeR-CFR方式）
        print("\n6. 测试阈值计算...")
        t_threshold, y_threshold = compute_thresholds(data_loader)
        print(f"处理变量阈值: {t_threshold}")
        print(f"结果变量阈值: {y_threshold}")

        # 验证潜在结果（使用原始数据，无预处理）
        print("\n7. 验证潜在结果...")
        train_potential_y = np.concatenate([train_data['mu0'], train_data['mu1']], axis=1)
        print(f"潜在结果矩阵形状: {train_potential_y.shape}")
        print(f"Y(0)范围: [{np.min(train_data['mu0']):.3f}, {np.max(train_data['mu0']):.3f}]")
        print(f"Y(1)范围: [{np.min(train_data['mu1']):.3f}, {np.max(train_data['mu1']):.3f}]")

        # 计算真实处理效果
        true_ite = train_data['mu1'] - train_data['mu0']
        print(f"真实ITE范围: [{np.min(true_ite):.3f}, {np.max(true_ite):.3f}]")
        print(f"真实ATE: {np.mean(true_ite):.6f}")
        
        print("\n" + "="*60)
        print("✅ NPZ数据加载测试成功! (DeR-CFR方式，无预处理)")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ NPZ数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_compatibility():
    """测试数据与模型的兼容性"""
    print("\n" + "="*60)
    print("测试数据与模型的兼容性")
    print("="*60)
    
    try:
        # 加载数据
        config = get_config()
        data_loader = load_twins_npz_data(
            data_path=config.data_path,
            train_valid_test=[63, 27, 10],  # DeR-CFR实际划分
            seed=config.seed,
            ind=0
        )

        # 更新配置
        data_info = get_npz_data_info(data_loader)
        config.x_dim = data_info['x_dim']

        # 计算阈值
        t_threshold, y_threshold = compute_thresholds(data_loader)
        config.t_threshold = t_threshold
        config.y_threshold = y_threshold

        # 直接使用原始数据（无预处理）
        train_data = data_loader.train

        # 构造模型输入格式
        train_x = train_data['x']
        train_t = train_data['t']
        train_y = train_data['y']
        train_potential_y = np.concatenate([train_data['mu0'], train_data['mu1']], axis=1)
        
        print(f"模型输入格式:")
        print(f"  train_x: {train_x.shape} (特征)")
        print(f"  train_t: {train_t.shape} (处理变量)")
        print(f"  train_y: {train_y.shape} (事实结果)")
        print(f"  train_potential_y: {train_potential_y.shape} (潜在结果)")
        
        # 验证数据类型
        print(f"\n数据类型:")
        print(f"  train_x: {train_x.dtype}")
        print(f"  train_t: {train_t.dtype}")
        print(f"  train_y: {train_y.dtype}")
        print(f"  train_potential_y: {train_potential_y.dtype}")
        
        # 验证数据范围
        print(f"\n数据范围:")
        print(f"  处理变量: [{np.min(train_t):.0f}, {np.max(train_t):.0f}]")
        print(f"  事实结果: [{np.min(train_y):.0f}, {np.max(train_y):.0f}]")
        print(f"  潜在结果Y(0): [{np.min(train_potential_y[:, 0]):.0f}, {np.max(train_potential_y[:, 0]):.0f}]")
        print(f"  潜在结果Y(1): [{np.min(train_potential_y[:, 1]):.0f}, {np.max(train_potential_y[:, 1]):.0f}]")
        
        print("\n✅ 数据与模型兼容性测试成功!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始NPZ数据加载测试...")
    
    # 测试数据加载
    success1 = test_npz_data_loading()
    
    # 测试兼容性
    success2 = test_data_compatibility()
    
    if success1 and success2:
        print("\n🎉 所有测试通过!")
        print("NPZ数据加载功能正常，可以开始训练模型。")
    else:
        print("\n❌ 测试失败，请检查数据文件和配置。")
