"""
分析训练集、验证集、测试集的数据分布差异
找出为什么测试集结果比训练集好的原因
"""

import numpy as np
import matplotlib.pyplot as plt
from npzdata import load_twins_npz_data
import seaborn as sns

def analyze_data_distribution():
    """分析数据分布差异"""
    print("="*60)
    print("分析训练集、验证集、测试集的数据分布")
    print("="*60)
    
    # 加载数据
    data_loader = load_twins_npz_data(
        data_path='../data28/Twins38.combined.npz',
        train_valid_test=[63, 27, 10],
        seed=123,
        ind=0
    )
    
    # 获取各数据集
    train_data = data_loader.train
    valid_data = data_loader.valid
    test_data = data_loader.test
    
    print("\n1. 基本统计信息:")
    print(f"训练集样本数: {len(train_data['x'])}")
    print(f"验证集样本数: {len(valid_data['x'])}")
    print(f"测试集样本数: {len(test_data['x'])}")
    
    # 分析处理变量分布
    print("\n2. 处理变量(t)分布:")
    train_t_ratio = np.mean(train_data['t'])
    valid_t_ratio = np.mean(valid_data['t'])
    test_t_ratio = np.mean(test_data['t'])
    
    print(f"训练集处理比例: {train_t_ratio:.4f}")
    print(f"验证集处理比例: {valid_t_ratio:.4f}")
    print(f"测试集处理比例: {test_t_ratio:.4f}")
    
    # 分析结果变量分布
    print("\n3. 结果变量(y)分布:")
    train_y_mean = np.mean(train_data['y'])
    valid_y_mean = np.mean(valid_data['y'])
    test_y_mean = np.mean(test_data['y'])
    
    print(f"训练集结果均值: {train_y_mean:.4f}")
    print(f"验证集结果均值: {valid_y_mean:.4f}")
    print(f"测试集结果均值: {test_y_mean:.4f}")
    
    # 分析真实ATE
    print("\n4. 真实ATE分布:")
    train_ate = np.mean(train_data['mu1'] - train_data['mu0'])
    valid_ate = np.mean(valid_data['mu1'] - valid_data['mu0'])
    test_ate = np.mean(test_data['mu1'] - test_data['mu0'])
    
    print(f"训练集真实ATE: {train_ate:.6f}")
    print(f"验证集真实ATE: {valid_ate:.6f}")
    print(f"测试集真实ATE: {test_ate:.6f}")
    
    # 分析潜在结果分布
    print("\n5. 潜在结果分布:")
    print("Y(0)分布:")
    print(f"  训练集: 均值={np.mean(train_data['mu0']):.4f}, 标准差={np.std(train_data['mu0']):.4f}")
    print(f"  验证集: 均值={np.mean(valid_data['mu0']):.4f}, 标准差={np.std(valid_data['mu0']):.4f}")
    print(f"  测试集: 均值={np.mean(test_data['mu0']):.4f}, 标准差={np.std(test_data['mu0']):.4f}")
    
    print("Y(1)分布:")
    print(f"  训练集: 均值={np.mean(train_data['mu1']):.4f}, 标准差={np.std(train_data['mu1']):.4f}")
    print(f"  验证集: 均值={np.mean(valid_data['mu1']):.4f}, 标准差={np.std(valid_data['mu1']):.4f}")
    print(f"  测试集: 均值={np.mean(test_data['mu1']):.4f}, 标准差={np.std(test_data['mu1']):.4f}")
    
    # 分析协变量分布
    print("\n6. 协变量分布差异:")
    train_x_mean = np.mean(train_data['x'], axis=0)
    valid_x_mean = np.mean(valid_data['x'], axis=0)
    test_x_mean = np.mean(test_data['x'], axis=0)
    
    # 计算各数据集与训练集的差异
    valid_x_diff = np.mean(np.abs(valid_x_mean - train_x_mean))
    test_x_diff = np.mean(np.abs(test_x_mean - train_x_mean))
    
    print(f"验证集与训练集协变量差异: {valid_x_diff:.6f}")
    print(f"测试集与训练集协变量差异: {test_x_diff:.6f}")
    
    # 找出差异最大的特征
    feature_diffs = np.abs(test_x_mean - train_x_mean)
    top_diff_features = np.argsort(feature_diffs)[-5:]
    
    print(f"\n差异最大的5个特征:")
    for i, feat_idx in enumerate(top_diff_features):
        print(f"  特征{feat_idx}: 训练集={train_x_mean[feat_idx]:.4f}, 测试集={test_x_mean[feat_idx]:.4f}, 差异={feature_diffs[feat_idx]:.4f}")
    
    # 分析个体处理效应分布
    print("\n7. 个体处理效应(ITE)分布:")
    train_ite = train_data['mu1'] - train_data['mu0']
    valid_ite = valid_data['mu1'] - valid_data['mu0']
    test_ite = test_data['mu1'] - test_data['mu0']
    
    print(f"训练集ITE: 均值={np.mean(train_ite):.6f}, 标准差={np.std(train_ite):.6f}")
    print(f"验证集ITE: 均值={np.mean(valid_ite):.6f}, 标准差={np.std(valid_ite):.6f}")
    print(f"测试集ITE: 均值={np.mean(test_ite):.6f}, 标准差={np.std(test_ite):.6f}")
    
    # 分析ITE的分布形状
    print(f"\nITE分布特征:")
    print(f"训练集ITE范围: [{np.min(train_ite):.4f}, {np.max(train_ite):.4f}]")
    print(f"验证集ITE范围: [{np.min(valid_ite):.4f}, {np.max(valid_ite):.4f}]")
    print(f"测试集ITE范围: [{np.min(test_ite):.4f}, {np.max(test_ite):.4f}]")
    
    # 分析数据难度
    print("\n8. 数据预测难度分析:")
    
    # 计算每个数据集的"噪声水平"
    def calculate_noise_level(mu0, mu1, y, t):
        """计算噪声水平：观测结果与真实潜在结果的差异"""
        true_y = t * mu1.flatten() + (1 - t) * mu0.flatten()
        noise = np.mean(np.abs(y.flatten() - true_y))
        return noise
    
    train_noise = calculate_noise_level(train_data['mu0'], train_data['mu1'], train_data['y'], train_data['t'])
    valid_noise = calculate_noise_level(valid_data['mu0'], valid_data['mu1'], valid_data['y'], valid_data['t'])
    test_noise = calculate_noise_level(test_data['mu0'], test_data['mu1'], test_data['y'], test_data['t'])
    
    print(f"训练集噪声水平: {train_noise:.6f}")
    print(f"验证集噪声水平: {valid_noise:.6f}")
    print(f"测试集噪声水平: {test_noise:.6f}")
    
    # 分析倾向性得分分布
    print("\n9. 倾向性得分分布:")
    if 'e' in train_data:
        train_ps_mean = np.mean(train_data['e'])
        valid_ps_mean = np.mean(valid_data['e'])
        test_ps_mean = np.mean(test_data['e'])
        
        print(f"训练集倾向性得分: {train_ps_mean:.4f}")
        print(f"验证集倾向性得分: {valid_ps_mean:.4f}")
        print(f"测试集倾向性得分: {test_ps_mean:.4f}")
    
    # 总结分析
    print("\n" + "="*60)
    print("数据分布分析总结")
    print("="*60)
    
    print(f"\n关键发现:")
    print(f"1. 真实ATE差异: 训练集({train_ate:.6f}) vs 测试集({test_ate:.6f})")
    print(f"2. 协变量分布差异: 测试集与训练集差异={test_x_diff:.6f}")
    print(f"3. 噪声水平差异: 训练集({train_noise:.6f}) vs 测试集({test_noise:.6f})")
    print(f"4. ITE标准差差异: 训练集({np.std(train_ite):.6f}) vs 测试集({np.std(test_ite):.6f})")
    
    # 判断可能的原因
    if test_noise < train_noise:
        print(f"\n⚠️ 可能原因: 测试集噪声更低，更容易预测")
    
    if np.std(test_ite) < np.std(train_ite):
        print(f"⚠️ 可能原因: 测试集ITE变异性更小，更容易预测")
    
    if abs(test_ate) > abs(train_ate):
        print(f"⚠️ 可能原因: 测试集ATE绝对值更大，信号更强")
    
    if test_x_diff > 0.01:
        print(f"⚠️ 可能原因: 测试集协变量分布与训练集差异较大")
    
    return {
        'train_ate': train_ate,
        'valid_ate': valid_ate,
        'test_ate': test_ate,
        'train_noise': train_noise,
        'valid_noise': valid_noise,
        'test_noise': test_noise,
        'test_x_diff': test_x_diff
    }

if __name__ == "__main__":
    results = analyze_data_distribution()
    print(f"\n分析完成！")
